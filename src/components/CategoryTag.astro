---
interface Props {
  name: string;
  type: 'category' | 'tag';
  size?: 'small' | 'medium' | 'large';
  href?: string;
}

const { name, type, size = 'medium', href } = Astro.props;
// Create URL-safe slug from name
const slug = name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');
const url = href || `/${type === 'category' ? 'categories' : 'tags'}/${slug}/`;
---

<a href={url} class={`tag ${type} ${size}`}>
  {type === 'category' && <span class="icon">📁</span>}
  {type === 'tag' && <span class="icon">#</span>}
  {name}
</a>

<style>
  .tag {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1px solid transparent;
  }

  .tag.small {
    padding: 0.125rem 0.5rem;
    font-size: 0.75rem;
  }

  .tag.large {
    padding: 0.5rem 1rem;
    font-size: 1rem;
  }

  .tag.category {
    background: var(--theme-color, #00a99d);
    color: white;
  }

  .tag.category:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 169, 157, 0.3);
    background: color-mix(in srgb, var(--theme-color, #00a99d), black 10%);
  }

  .tag.tag {
    background: color-mix(in srgb, var(--theme-color, #00a99d), transparent 10%);
    color: white;
    border: 1px solid var(--theme-color, #00a99d);
  }

  .tag.tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 169, 157, 0.3);
    background: var(--theme-color, #00a99d);
  }

  .icon {
    font-size: 0.875em;
  }

  .tag.small .icon {
    font-size: 0.75em;
  }

  .tag.large .icon {
    font-size: 1em;
  }
</style>
