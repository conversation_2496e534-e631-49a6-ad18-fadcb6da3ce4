---
interface Props {
  testName: string;
  variants: {
    name: string;
    weight: number; // 0-100
    component?: any;
    content?: string;
  }[];
  defaultVariant?: string;
}

const { testName, variants, defaultVariant } = Astro.props;

// Ensure weights add up to 100
const totalWeight = variants.reduce((sum, variant) => sum + variant.weight, 0);
if (totalWeight !== 100) {
  console.warn(`A/B Test "${testName}": Weights don't add up to 100 (${totalWeight})`);
}
---

<div class="ab-test" data-test-name={testName}>
  <div id={`ab-test-${testName}`} class="ab-test-container">
    <!-- Variants will be loaded here by JavaScript -->
  </div>
</div>

<script define:vars={{ testName, variants, defaultVariant }}>
  class ABTestManager {
    constructor(testName, variants, defaultVariant) {
      this.testName = testName;
      this.variants = variants;
      this.defaultVariant = defaultVariant || variants[0]?.name;
      this.storageKey = `ab-test-${testName}`;
      this.init();
    }

    init() {
      const selectedVariant = this.getOrAssignVariant();
      this.renderVariant(selectedVariant);
      this.trackVariantView(selectedVariant);
    }

    getOrAssignVariant() {
      // Check if user already has an assigned variant
      let assignedVariant = localStorage.getItem(this.storageKey);
      
      if (assignedVariant && this.variants.find(v => v.name === assignedVariant)) {
        return assignedVariant;
      }

      // Assign new variant based on weights
      const random = Math.random() * 100;
      let cumulativeWeight = 0;

      for (const variant of this.variants) {
        cumulativeWeight += variant.weight;
        if (random <= cumulativeWeight) {
          assignedVariant = variant.name;
          break;
        }
      }

      // Fallback to default
      if (!assignedVariant) {
        assignedVariant = this.defaultVariant;
      }

      // Store assignment
      localStorage.setItem(this.storageKey, assignedVariant);
      
      // Also store in session for analytics
      sessionStorage.setItem(`${this.storageKey}-session`, JSON.stringify({
        variant: assignedVariant,
        assignedAt: new Date().toISOString(),
        testName: this.testName
      }));

      return assignedVariant;
    }

    renderVariant(variantName) {
      const variant = this.variants.find(v => v.name === variantName);
      if (!variant) return;

      const container = document.getElementById(`ab-test-${this.testName}`);
      if (!container) return;

      // Add variant class for CSS targeting
      container.className = `ab-test-container variant-${variantName}`;
      
      // Render content
      if (variant.content) {
        container.innerHTML = variant.content;
      }

      // Add data attribute for analytics
      container.setAttribute('data-variant', variantName);
    }

    trackVariantView(variantName) {
      // Track variant exposure
      this.trackEvent('variant_view', {
        test_name: this.testName,
        variant: variantName,
        timestamp: new Date().toISOString()
      });
    }

    trackConversion(conversionType = 'default', value = 1) {
      const variantName = localStorage.getItem(this.storageKey);
      if (!variantName) return;

      this.trackEvent('conversion', {
        test_name: this.testName,
        variant: variantName,
        conversion_type: conversionType,
        value: value,
        timestamp: new Date().toISOString()
      });
    }

    async trackEvent(eventType, data) {
      try {
        await fetch('/api/ab-testing', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            event_type: eventType,
            ...data
          })
        });
      } catch (error) {
        console.warn('A/B test tracking failed:', error);
      }
    }

    // Static method to track conversions from anywhere
    static trackConversion(testName, conversionType = 'default', value = 1) {
      const variantName = localStorage.getItem(`ab-test-${testName}`);
      if (!variantName) return;

      fetch('/api/ab-testing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          event_type: 'conversion',
          test_name: testName,
          variant: variantName,
          conversion_type: conversionType,
          value: value,
          timestamp: new Date().toISOString()
        })
      }).catch(error => console.warn('A/B test tracking failed:', error));
    }
  }

  // Initialize the test
  new ABTestManager(testName, variants, defaultVariant);

  // Make conversion tracking available globally
  window.ABTest = {
    trackConversion: ABTestManager.trackConversion
  };
</script>

<style>
  .ab-test {
    /* Base styles for A/B test container */
  }

  .ab-test-container {
    /* Prevent layout shift during variant loading */
    min-height: 1px;
  }

  /* Variant-specific styles can be added here */
  .variant-control {
    /* Styles for control variant */
  }

  .variant-treatment {
    /* Styles for treatment variant */
  }

  /* Hide content until variant is loaded to prevent flash */
  .ab-test-container:empty {
    opacity: 0;
  }

  .ab-test-container:not(:empty) {
    opacity: 1;
    transition: opacity 0.2s ease;
  }
</style>
