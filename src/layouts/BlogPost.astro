---
import type { CollectionEntry } from 'astro:content';
import Layout from './Layout.astro';
import FormattedDate from '../components/FormattedDate.astro';
import CategoryTag from '../components/CategoryTag.astro';
import SocialShare from '../components/SocialShare.astro';
import SchemaMarkup from '../components/SchemaMarkup.astro';
import Comments from '../components/Comments.astro';
import Newsletter from '../components/Newsletter.astro';
import Analytics from '../components/Analytics.astro';
import { getRelatedPosts } from '../utils/blog';

type Props = CollectionEntry<'blog'>['data'] & {
  slug: string;
};

const { title, description, pubDate, updatedDate, heroImage, category, tags, author, slug } = Astro.props;

// Get related posts
const currentPost = { data: Astro.props, slug } as CollectionEntry<'blog'>;
const relatedPosts = await getRelatedPosts(currentPost, 3);
---

<Layout title={title} description={description} image={heroImage}>
	<SchemaMarkup
		type="article"
		title={title}
		description={description}
		url={Astro.url.pathname}
		image={heroImage}
		author={author}
		publishDate={pubDate}
		modifiedDate={updatedDate}
		category={category}
		tags={tags}
	/>
	<Analytics postId={slug} />
	<main class="blog-post-main">
		<div class="container">
			<a href="/blog" class="back-link">← Torna al Blog</a>
			<article class="blog-post">
				<div class="hero-image">
					{heroImage && <img width={1020} height={510} src={heroImage} alt={title} />}
				</div>
				<div class="prose">
					<div class="title">
						<div class="post-meta">
							<div class="date-info">
								<FormattedDate date={pubDate} />
								{
									updatedDate && (
										<div class="last-updated-on">
											Aggiornato il <FormattedDate date={updatedDate} />
										</div>
									)
								}
							</div>
							{author && (
								<div class="author-info">
									di <strong>{author}</strong>
								</div>
							)}
						</div>
						<h1>{title}</h1>

						<!-- Categories and Tags -->
						<div class="post-taxonomy">
							{category && (
								<div class="taxonomy-group">
									<span class="taxonomy-label">Categoria:</span>
									<CategoryTag name={category} type="category" size="medium" />
								</div>
							)}
							{tags && tags.length > 0 && (
								<div class="taxonomy-group">
									<span class="taxonomy-label">Tag:</span>
									<div class="tags-list">
										{tags.map(tag => (
											<CategoryTag name={tag} type="tag" size="small" />
										))}
									</div>
								</div>
							)}
						</div>
						<hr />
					</div>
					<slot />

					<!-- Social Share Section -->
					<SocialShare
						title={title}
						description={description}
						url={Astro.url.href}
					/>

					<!-- Related Posts Section -->
					{relatedPosts.length > 0 && (
						<div class="related-posts">
							<h3>Articoli Correlati</h3>
							<div class="related-posts-grid">
								{relatedPosts.map(post => (
									<article class="related-post">
										<a href={`/blog/${post.slug}/`}>
											{post.data.heroImage && <img src={post.data.heroImage} alt={post.data.title} />}
											<div class="related-post-content">
												<h4>{post.data.title}</h4>
												<p>{post.data.description}</p>
												<div class="related-post-meta">
													<FormattedDate date={post.data.pubDate} />
												</div>
											</div>
										</a>
									</article>
								))}
							</div>
						</div>
					)}

					<!-- Newsletter Signup -->
					<Newsletter />

					<!-- Comments Section -->
					<Comments repo="mbiagiottime/BlogMb" />
				</div>
			</article>
		</div>
	</main>
</Layout>

<style>
	.blog-post-main {
		padding: 2rem 0;
		background: var(--background-color);
	}

	.container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 1rem;
	}

	.back-link {
		display: inline-flex;
		align-items: center;
		gap: 0.5rem;
		margin-bottom: 2rem;
		text-decoration: none;
		background-color: var(--theme-color);
		color: white;
		padding: 0.75rem 1.5rem;
		border-radius: 25px;
		transition: all 0.3s ease;
		font-weight: 500;
	}

	.back-link:hover {
		background-color: var(--theme-color);
		opacity: 0.9;
		transform: translateX(-5px);
	}

	.blog-post {
		background: var(--surface-color);
		border-radius: 15px;
		overflow: hidden;
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
	}

	.hero-image {
		width: 100%;
		height: 400px;
		overflow: hidden;
	}

	.hero-image img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.prose {
		max-width: 800px;
		margin: 0 auto;
		padding: 3rem 2rem;
		color: var(--default-color);
		line-height: 1.7;
	}

	.title {
		margin-bottom: 2rem;
		padding-bottom: 2rem;
		text-align: center;
		border-bottom: 2px solid rgba(var(--theme-color-rgb), 0.1);
	}

	.title h1 {
		font-size: 2.5rem;
		font-weight: 700;
		margin: 1rem 0;
		color: var(--heading-color);
		line-height: 1.2;
	}

	.post-meta {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 1rem;
		flex-wrap: wrap;
		gap: 1rem;
		font-size: 0.95rem;
	}

	.date-info {
		color: var(--gray);
	}

	.last-updated-on {
		font-style: italic;
		margin-top: 0.25rem;
	}

	.author-info {
		color: var(--gray);
	}

	.post-taxonomy {
		margin: 1.5rem 0;
	}

	.taxonomy-group {
		display: flex;
		align-items: center;
		gap: 0.75rem;
		margin-bottom: 1rem;
		flex-wrap: wrap;
		justify-content: center;
	}

	.taxonomy-label {
		font-weight: 600;
		color: var(--heading-color);
		font-size: 0.9rem;
	}

	.tags-list {
		display: flex;
		flex-wrap: wrap;
		gap: 0.5rem;
		justify-content: center;
	}

	.related-posts {
		margin-top: 3rem;
		padding-top: 2rem;
		border-top: 2px solid rgba(var(--theme-color-rgb), 0.1);
	}

	.related-posts h3 {
		margin-bottom: 1.5rem;
		color: var(--heading-color);
		text-align: center;
		font-size: 1.8rem;
	}

	.related-posts-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 1.5rem;
	}

	.related-post {
		background: var(--surface-color);
		border-radius: 10px;
		overflow: hidden;
		box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
		transition: transform 0.3s ease, box-shadow 0.3s ease;
	}

	.related-post:hover {
		transform: translateY(-5px);
		box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
	}

	.related-post a {
		text-decoration: none;
		color: inherit;
		display: block;
	}

	.related-post img {
		width: 100%;
		height: 150px;
		object-fit: cover;
	}

	.related-post-content {
		padding: 1.25rem;
	}

	.related-post h4 {
		margin: 0 0 0.75rem 0;
		font-size: 1.1rem;
		line-height: 1.3;
		color: var(--heading-color);
		font-weight: 600;
	}

	.related-post p {
		margin: 0 0 0.75rem 0;
		font-size: 0.9rem;
		color: var(--default-color);
		line-height: 1.4;
	}

	.related-post-meta {
		font-size: 0.85rem;
		color: var(--gray);
	}

	@media (max-width: 768px) {
		.prose {
			padding: 2rem 1rem;
		}

		.title h1 {
			font-size: 2rem;
		}

		.post-meta {
			flex-direction: column;
			gap: 0.5rem;
			text-align: center;
		}

		.taxonomy-group {
			flex-direction: column;
			align-items: center;
			gap: 0.5rem;
		}

		.related-posts-grid {
			grid-template-columns: 1fr;
		}

		.hero-image {
			height: 250px;
		}
	}
</style>