---
// Unified header component with fullscreen mobile menu
---

<header id="header" class="header d-flex align-items-center sticky-top">
  <div class="container-fluid container-xl position-relative d-flex align-items-center">

    <a href="/" class="logo d-flex align-items-center me-auto">
      <img src="/logo.png" alt="<PERSON>" class="sitename">
    </a>

    <!-- Desktop Navigation -->
    <nav id="navmenu" class="navmenu d-none d-xl-flex">
      <ul>
        <li><a href="/#about" class="nav-link">Chi Sono</a></li>
        <li><a href="/#services" class="nav-link">Servizi</a></li>
        <li><a href="/#skills" class="nav-link">Competenze</a></li>

        <!-- Dropdown: Esperienza -->
        <li class="has-dropdown">
          <a href="/#experience" class="nav-link">Esperienza</a>
          <ul class="dropdown-menu">
            <li><a href="/#experience">Esperienze Professionali</a></li>
            <li><a href="/#google-expert">Google Expert</a></li>
            <li><a href="/#automotive">Settore Automotive</a></li>
            <li><a href="/#speaker">Relatore in Conferenze</a></li>
          </ul>
        </li>

        <!-- Dropdown: Formazione -->
        <li class="has-dropdown">
          <a href="/#publications" class="nav-link">Formazione</a>
          <ul class="dropdown-menu">
            <li><a href="/#publications">Pubblicazioni</a></li>
            <li><a href="/#courses">Corsi</a></li>
          </ul>
        </li>

        <!-- Blog Link -->
        <li><a href="/blog" class="nav-link">Blog</a></li>

        <li><a href="/#contact" class="nav-link">Contatti</a></li>
      </ul>
    </nav>

    <!-- Desktop CTA Button -->
    <a class="btn-getstarted d-none d-xl-block" href="/#contact">Parliamo del tuo Progetto</a>

    <!-- Mobile Menu Toggle -->
    <button class="mobile-menu-toggle d-xl-none" type="button" aria-label="Toggle mobile menu">
      <span class="hamburger-line"></span>
      <span class="hamburger-line"></span>
      <span class="hamburger-line"></span>
    </button>

  </div>
</header>

<!-- Mobile Menu Modal -->
<div class="mobile-menu-overlay" id="mobileMenuOverlay">
  <div class="mobile-menu-container">
    <div class="mobile-menu-header">
      <a href="/" class="mobile-logo">
        <img src="/logo.png" alt="Marco Biagiotti">
      </a>
      <button class="mobile-menu-close" type="button" aria-label="Close mobile menu">
        <i class="bi bi-x"></i>
      </button>
    </div>

    <nav class="mobile-menu-nav">
      <ul class="mobile-menu-list">
        <li><a href="/#about" class="mobile-nav-link">Chi Sono</a></li>
        <li><a href="/#services" class="mobile-nav-link">Servizi</a></li>
        <li><a href="/#skills" class="mobile-nav-link">Competenze</a></li>

        <!-- Mobile Dropdown: Esperienza -->
        <li class="mobile-dropdown">
          <button class="mobile-dropdown-toggle" type="button">
            Esperienza
            <i class="bi bi-chevron-down"></i>
          </button>
          <ul class="mobile-dropdown-menu">
            <li><a href="/#experience" class="mobile-nav-link">Esperienze Professionali</a></li>
            <li><a href="/#google-expert" class="mobile-nav-link">Google Expert</a></li>
            <li><a href="/#automotive" class="mobile-nav-link">Settore Automotive</a></li>
            <li><a href="/#speaker" class="mobile-nav-link">Relatore in Conferenze</a></li>
          </ul>
        </li>

        <!-- Mobile Dropdown: Formazione -->
        <li class="mobile-dropdown">
          <button class="mobile-dropdown-toggle" type="button">
            Formazione
            <i class="bi bi-chevron-down"></i>
          </button>
          <ul class="mobile-dropdown-menu">
            <li><a href="/#publications" class="mobile-nav-link">Pubblicazioni</a></li>
            <li><a href="/#courses" class="mobile-nav-link">Corsi</a></li>
          </ul>
        </li>

        <li><a href="/blog" class="mobile-nav-link">Blog</a></li>
        <li><a href="/#contact" class="mobile-nav-link">Contatti</a></li>
      </ul>

      <!-- Mobile CTA Button -->
      <div class="mobile-menu-cta">
        <a href="/#contact" class="btn-mobile-cta">Parliamo del tuo Progetto</a>
      </div>
    </nav>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Theme color functionality
    const style = getComputedStyle(document.documentElement);
    const randomTheme = style.getPropertyValue('--random-theme').trim();

    if (randomTheme === 'yes') {
      const logoColors = [
        '--logo-blue', '--logo-light-blue', '--logo-teal',
        '--logo-green', '--logo-light-green', '--logo-purple'
      ];

      const logoColorsRGB = {
        '--logo-blue': '0, 113, 188',
        '--logo-light-blue': '41, 171, 226',
        '--logo-teal': '0, 169, 157',
        '--logo-green': '57, 181, 74',
        '--logo-light-green': '140, 198, 63',
        '--logo-purple': '92, 56, 150'
      };

      const randomColor = logoColors[Math.floor(Math.random() * logoColors.length)];
      document.documentElement.style.setProperty('--theme-color', `var(${randomColor})`);
      document.documentElement.style.setProperty('--theme-color-rgb', logoColorsRGB[randomColor]);
    }

    // Replace primary badges with theme badges
    document.querySelectorAll('.badge.bg-primary').forEach(badge => {
      badge.classList.remove('bg-primary');
      badge.classList.add('badge-theme');
    });

    // Mobile Menu Elements
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const mobileMenuOverlay = document.querySelector('.mobile-menu-overlay');
    const mobileMenuClose = document.querySelector('.mobile-menu-close');
    const mobileDropdownToggles = document.querySelectorAll('.mobile-dropdown-toggle');
    const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');

    // Open mobile menu
    if (mobileMenuToggle) {
      mobileMenuToggle.addEventListener('click', function() {
        mobileMenuOverlay.classList.add('active');
        document.body.classList.add('mobile-menu-open');
        this.classList.add('active');
      });
    }

    // Close mobile menu
    function closeMobileMenu() {
      mobileMenuOverlay.classList.remove('active');
      document.body.classList.remove('mobile-menu-open');
      mobileMenuToggle.classList.remove('active');

      // Close all dropdowns
      document.querySelectorAll('.mobile-dropdown').forEach(dropdown => {
        dropdown.classList.remove('active');
      });
    }

    if (mobileMenuClose) {
      mobileMenuClose.addEventListener('click', closeMobileMenu);
    }

    // Close menu when clicking overlay
    if (mobileMenuOverlay) {
      mobileMenuOverlay.addEventListener('click', function(e) {
        if (e.target === this) {
          closeMobileMenu();
        }
      });
    }

    // Mobile dropdown toggles
    mobileDropdownToggles.forEach(toggle => {
      toggle.addEventListener('click', function() {
        const dropdown = this.parentElement;
        const isActive = dropdown.classList.contains('active');

        // Close all other dropdowns
        document.querySelectorAll('.mobile-dropdown').forEach(d => {
          if (d !== dropdown) d.classList.remove('active');
        });

        // Toggle current dropdown
        dropdown.classList.toggle('active', !isActive);
      });
    });

    // Close menu when clicking on links
    mobileNavLinks.forEach(link => {
      link.addEventListener('click', closeMobileMenu);
    });

    // Close menu on escape key
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape' && mobileMenuOverlay.classList.contains('active')) {
        closeMobileMenu();
      }
    });

    // Set active navigation links
    const currentPath = window.location.pathname;
    document.querySelectorAll('.nav-link, .mobile-nav-link').forEach(link => {
      const href = link.getAttribute('href');
      if (href === currentPath || (currentPath === '/' && href === '/#about')) {
        link.classList.add('active');
      }
    });
  });
</script>

<style>
/* Mobile Menu Toggle Button */
.mobile-menu-toggle {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  position: relative;
  z-index: 1001;
}

.hamburger-line {
  width: 24px;
  height: 2px;
  background-color: var(--nav-color);
  margin: 3px 0;
  transition: all 0.3s ease;
  transform-origin: center;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-menu-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Mobile Menu Container */
.mobile-menu-container {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  max-width: 400px;
  height: 100%;
  background: var(--background-color);
  transform: translateX(100%);
  transition: transform 0.3s ease;
  overflow-y: auto;
}

.mobile-menu-overlay.active .mobile-menu-container {
  transform: translateX(0);
}

/* Mobile Menu Header */
.mobile-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

.mobile-logo img {
  height: 36px;
}

.mobile-menu-close {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--nav-color);
  cursor: pointer;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Mobile Menu Navigation */
.mobile-menu-nav {
  padding: 20px 0;
}

.mobile-menu-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.mobile-menu-list > li {
  border-bottom: 1px solid color-mix(in srgb, var(--default-color), transparent 95%);
}

.mobile-nav-link {
  display: block;
  padding: 16px 20px;
  color: var(--nav-color);
  text-decoration: none;
  font-family: var(--nav-font);
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
  color: var(--nav-hover-color);
  background: color-mix(in srgb, var(--accent-color), transparent 95%);
}

/* Mobile Dropdown */
.mobile-dropdown-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 16px 20px;
  background: none;
  border: none;
  color: var(--nav-color);
  font-family: var(--nav-font);
  font-size: 16px;
  font-weight: 500;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mobile-dropdown-toggle:hover {
  color: var(--nav-hover-color);
  background: color-mix(in srgb, var(--accent-color), transparent 95%);
}

.mobile-dropdown-toggle i {
  transition: transform 0.3s ease;
}

.mobile-dropdown.active .mobile-dropdown-toggle i {
  transform: rotate(180deg);
}

.mobile-dropdown-menu {
  list-style: none;
  margin: 0;
  padding: 0;
  background: color-mix(in srgb, var(--default-color), transparent 98%);
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.mobile-dropdown.active .mobile-dropdown-menu {
  max-height: 300px;
}

.mobile-dropdown-menu .mobile-nav-link {
  padding-left: 40px;
  font-size: 15px;
}

/* Mobile CTA */
.mobile-menu-cta {
  padding: 20px;
  margin-top: 20px;
}

.btn-mobile-cta {
  display: block;
  width: 100%;
  padding: 12px 20px;
  background: var(--accent-color);
  color: var(--contrast-color);
  text-decoration: none;
  text-align: center;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-mobile-cta:hover {
  background: color-mix(in srgb, var(--accent-color), black 10%);
  color: var(--contrast-color);
}

/* Prevent body scroll when menu is open */
body.mobile-menu-open {
  overflow: hidden;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .mobile-menu-container {
    max-width: 100%;
  }
}
</style>
