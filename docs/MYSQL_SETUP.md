# 🗄️ Configurazione MySQL per <PERSON> Blog

Questa guida ti aiuterà a configurare MySQL per gestire newsletter, contatti e commenti del blog.

## 📋 Prerequisiti

- **MySQL Server** 5.7+ o **MariaDB** 10.3+
- **Node.js** 18+ con npm
- Accesso al server MySQL (locale o remoto)
- **Astro con output hybrid** configurato

## 🏗️ Architettura Database

Il blog utilizza **MySQL** per:
- **Newsletter subscriptions** - Gestione iscritti newsletter
- **Contact submissions** - Messaggi dal form contatti
- **Analytics data** - Statistiche personalizzate (opzionale)

**API Routes Hybrid**:
- `/api/newsletter` - Server-side per iscrizioni
- `/api/contact` - Server-side per messaggi

## 🚀 Setup Rapido

### 1. **Crea il Database**

Accedi a MySQL e crea il database:

```sql
-- Accedi a MySQL
mysql -u root -p

-- Crea il database
CREATE DATABASE biagiotti_blog CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Crea un utente dedicato (opzionale ma raccomandato)
CREATE USER 'biagiotti_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON biagiotti_blog.* TO 'biagiotti_user'@'localhost';
FLUSH PRIVILEGES;

-- Usa il database
USE biagiotti_blog;
```

### 2. **Importa lo Schema**

Esegui lo script SQL fornito:

```bash
# Dalla root del progetto
mysql -u root -p biagiotti_blog < database/schema.sql
```

Oppure copia e incolla il contenuto di `database/schema.sql` nel tuo client MySQL.

### 3. **Configura le Variabili d'Ambiente**

Copia il file di esempio:

```bash
cp .env.example .env
```

Modifica `.env` con le tue credenziali:

```env
# Database Configuration (MySQL)
DB_HOST=localhost
DB_USER=biagiotti_user
DB_PASSWORD=your_secure_password
DB_NAME=biagiotti_blog
DB_PORT=3306
DB_SSL=false

# SMTP Configuration
SMTP_HOST=mail.biagiotti.me
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password
SMTP_FROM=<EMAIL>
```

## 📊 Tabelle Create

Lo script crea automaticamente queste tabelle:

### **newsletter_subscriptions**
- Gestisce le iscrizioni alla newsletter
- Campi: id, email, name, status, subscribed_at, unsubscribed_at

### **contact_submissions**
- Salva i messaggi dal form di contatto
- Campi: id, name, email, subject, message, status, submitted_at

### **blog_comments**
- Commenti del blog (se non usi Utterances)
- Campi: id, post_slug, author_name, author_email, content, status, created_at

### **page_views**
- Analytics semplici (opzionale)
- Campi: id, page_path, user_agent, ip_address, referrer, viewed_at

### **email_logs**
- Log delle email inviate
- Campi: id, to_email, subject, template_name, status, sent_at

## 🔧 Funzionalità Avanzate

### **Views Create**
- `active_subscribers` - Iscritti attivi alla newsletter
- `recent_contacts` - Ultimi messaggi di contatto
- `approved_comments` - Commenti approvati per post

### **Stored Procedures**
- `CleanOldData()` - Pulisce dati vecchi automaticamente

### **Functions**
- `GetSubscriberCount()` - Conta iscritti attivi

### **Triggers**
- Aggiorna automaticamente `unsubscribed_at` quando qualcuno si disiscrive

## 🧪 Test della Configurazione

### **Test Connessione Database**

Crea un file di test `test-db.js`:

```javascript
import { connectDB, initializeDatabase } from './src/utils/database.js';

async function testDatabase() {
  console.log('🧪 Testing database connection...');
  
  const connected = await connectDB();
  if (connected) {
    console.log('✅ Database connection successful!');
    
    const initialized = await initializeDatabase();
    if (initialized) {
      console.log('✅ Database tables initialized!');
    }
  } else {
    console.log('❌ Database connection failed!');
  }
}

testDatabase();
```

Esegui il test:

```bash
node test-db.js
```

### **Test Newsletter**

Testa l'iscrizione alla newsletter:

```bash
curl -X POST http://localhost:4321/api/newsletter \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","name":"Test User"}'
```

### **Test Contatti**

Testa il form di contatto:

```bash
curl -X POST http://localhost:4321/api/contact \
  -H "Content-Type: application/json" \
  -d '{"name":"Test User","email":"<EMAIL>","message":"Test message"}'
```

## 🔒 Sicurezza

### **Best Practices**

1. **Usa un utente dedicato** per il database, non root
2. **Password sicure** con caratteri speciali
3. **Connessioni SSL** in produzione
4. **Backup regolari** del database
5. **Limita connessioni** per IP se possibile

### **Configurazione SSL**

Per connessioni sicure in produzione:

```env
DB_SSL=true
```

Assicurati che il tuo server MySQL supporti SSL.

## 📈 Monitoraggio

### **Query Utili**

```sql
-- Conta iscritti attivi
SELECT COUNT(*) as active_subscribers 
FROM newsletter_subscriptions 
WHERE status = 'active';

-- Ultimi 10 contatti
SELECT name, email, subject, submitted_at 
FROM contact_submissions 
ORDER BY submitted_at DESC 
LIMIT 10;

-- Statistiche email inviate
SELECT template_name, COUNT(*) as sent_count 
FROM email_logs 
WHERE status = 'sent' 
GROUP BY template_name;

-- Pulizia dati vecchi
CALL CleanOldData();
```

### **Backup Automatico**

Crea uno script di backup:

```bash
#!/bin/bash
# backup-db.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/path/to/backups"
DB_NAME="biagiotti_blog"

mysqldump -u biagiotti_user -p $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# Mantieni solo gli ultimi 7 backup
find $BACKUP_DIR -name "backup_*.sql" -mtime +7 -delete
```

## 🚨 Troubleshooting

### **Errore di Connessione**

```
Error: connect ECONNREFUSED 127.0.0.1:3306
```

**Soluzioni**:
1. Verifica che MySQL sia in esecuzione
2. Controlla host e porta in `.env`
3. Verifica firewall e permessi

### **Errore di Autenticazione**

```
Error: Access denied for user 'user'@'localhost'
```

**Soluzioni**:
1. Verifica username e password
2. Controlla i permessi dell'utente
3. Verifica che l'utente possa connettersi da localhost

### **Errore SSL**

```
Error: SSL connection error
```

**Soluzioni**:
1. Imposta `DB_SSL=false` per test locali
2. Configura certificati SSL per produzione
3. Verifica supporto SSL del server

## 📞 Supporto

Per problemi con la configurazione MySQL:

1. **Controlla i log** di MySQL: `/var/log/mysql/error.log`
2. **Verifica la configurazione** in `/etc/mysql/mysql.conf.d/mysqld.cnf`
3. **Testa la connessione** con un client MySQL
4. **Consulta la documentazione** MySQL ufficiale

---

**Configurazione completata!** 🎉

Il tuo blog ora può gestire newsletter, contatti e commenti con MySQL.
