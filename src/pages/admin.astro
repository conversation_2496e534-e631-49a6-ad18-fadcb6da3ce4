---
import Layout from '../layouts/Layout.astro';

const title = "Admin - Marco <PERSON> Blog";
const description = "Pannello di amministrazione del blog";
---

<Layout title={title} description={description}>
  <main class="admin-main">
    <div class="container">
      <div class="admin-content">
        <div class="admin-card">
          <div class="icon">
            <i class="bi bi-gear-fill"></i>
          </div>
          <h1>Pannello Admin</h1>
          <p class="lead">Il pannello di amministrazione non è ancora disponibile.</p>
          <p>Per gestire i contenuti del blog, puoi:</p>
          
          <div class="options">
            <div class="option">
              <h3>📝 Modifica diretta</h3>
              <p>Aggiungi o modifica i file Markdown nella cartella <code>src/content/blog/</code></p>
            </div>
            
            <div class="option">
              <h3>🔧 CMS Esterno</h3>
              <p>Integra un CMS headless come Strapi, Sanity o Contentful</p>
            </div>
            
            <div class="option">
              <h3>📁 Git-based</h3>
              <p>Usa Netlify CMS, Forestry o Decap CMS per gestire i contenuti via Git</p>
            </div>
          </div>
          
          <div class="actions">
            <a href="/blog" class="btn btn-theme">Vai al Blog</a>
            <a href="/" class="btn btn-outline-theme">Torna alla Home</a>
          </div>
        </div>
      </div>
    </div>
  </main>
</Layout>

<style>
  .admin-main {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  }

  .container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  .admin-content {
    text-align: center;
  }

  .admin-card {
    background: white;
    border-radius: 1.5rem;
    padding: 3rem 2rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  }

  .icon {
    font-size: 4rem;
    color: var(--theme-color);
    margin-bottom: 1.5rem;
  }

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--heading-color);
  }

  .lead {
    font-size: 1.2rem;
    color: var(--default-color);
    margin-bottom: 2rem;
    opacity: 0.8;
  }

  .options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
    text-align: left;
  }

  .option {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 1rem;
    border: 2px solid transparent;
    transition: all 0.3s ease;
  }

  .option:hover {
    border-color: var(--theme-color);
    transform: translateY(-4px);
  }

  .option h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--heading-color);
  }

  .option p {
    font-size: 0.9rem;
    color: var(--default-color);
    margin: 0;
    line-height: 1.5;
  }

  code {
    background: #e9ecef;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-family: 'Courier New', monospace;
    font-size: 0.85em;
  }

  .actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    flex-wrap: wrap;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-theme {
    background: var(--theme-color);
    color: white;
    border: 2px solid var(--theme-color);
  }

  .btn-theme:hover {
    background: color-mix(in srgb, var(--theme-color), black 10%);
    transform: translateY(-2px);
  }

  .btn-outline-theme {
    background: transparent;
    color: var(--theme-color);
    border: 2px solid var(--theme-color);
  }

  .btn-outline-theme:hover {
    background: var(--theme-color);
    color: white;
    transform: translateY(-2px);
  }

  @media (max-width: 768px) {
    .admin-card {
      padding: 2rem 1.5rem;
    }

    h1 {
      font-size: 2rem;
    }

    .options {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .actions {
      flex-direction: column;
      align-items: center;
    }

    .btn {
      width: 100%;
      max-width: 200px;
      justify-content: center;
    }
  }
</style>
