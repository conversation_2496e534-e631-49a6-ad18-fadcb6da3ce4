# Strapi Blog Sync Tool

Questo tool ti permette di sincronizzare automaticamente gli articoli dal tuo Strapi CMS (`contents.biagiotti.me`) al tuo blog Astro, convertendoli in file Markdown con tutti i metadati SEO.

## 🚀 Setup Iniziale

### 1. Installa le dipendenze
```bash
# Se non hai già fetch (Node.js 18+)
npm install node-fetch
```

### 2. Configura il token API Strapi

#### Opzione A: Variabile d'ambiente (Consigliata)
```bash
export STRAPI_API_TOKEN="your-strapi-api-token-here"
```

#### Opzione B: File .env
Crea un file `.env` nella root del progetto:
```env
STRAPI_API_TOKEN=your-strapi-api-token-here
```

### 3. Come ottenere il token API Strapi

1. Accedi al tuo pannello admin Strapi: `https://contents.biagiotti.me/admin`
2. Vai su **Settings** → **API Tokens**
3. Clicca **Create new API Token**
4. Configura:
   - **Name**: `Blog Sync Tool`
   - **Description**: `Token per sincronizzazione blog`
   - **Token duration**: `Unlimited` (o come preferisci)
   - **Token type**: `Read-only` (sufficiente per la sincronizzazione)
5. Copia il token generato

## 📋 Comandi Disponibili

### Sincronizza tutti gli articoli
```bash
node scripts/strapi-sync.js sync
```

### Sincronizza un articolo specifico
```bash
node scripts/strapi-sync.js sync 123
```
(dove `123` è l'ID dell'articolo in Strapi)

### Lista tutti gli articoli disponibili
```bash
node scripts/strapi-sync.js list
```

### Mostra l'aiuto
```bash
node scripts/strapi-sync.js help
```

## ⚙️ Configurazione

### File di configurazione
Modifica `scripts/strapi-config.js` per personalizzare:

- **URL Strapi**: Cambia `baseUrl` se diverso
- **Mapping campi**: Personalizza come i campi Strapi vengono mappati
- **Gestione immagini**: Configura download locale o URL remoti
- **Filtri**: Applica filtri per data, categoria, ecc.
- **Output**: Personalizza directory e formato file

### Esempio configurazione personalizzata
```javascript
export const STRAPI_CONFIG = {
  baseUrl: 'https://contents.biagiotti.me',
  
  // Scarica immagini localmente
  richTextConfig: {
    imageConfig: {
      downloadImages: true,
      localImageDir: 'public/blog-images'
    }
  },
  
  // Filtra solo articoli degli ultimi 6 mesi
  filters: {
    dateFilter: {
      enabled: true,
      from: '2024-01-01'
    }
  }
};
```

## 📁 Struttura File Generati

Gli articoli vengono salvati in `src/content/blog/` con questa struttura:

```markdown
---
title: 'Titolo dell'articolo'
description: 'Sommario dell'articolo'
pubDate: '2024-01-15'
heroImage: '/blog-images/immagine.jpg'
tags: ['tag1', 'tag2']
author: 'Marco Biagiotti'
category: 'Blog'
seoTitle: 'Titolo SEO personalizzato'
seoDescription: 'Descrizione SEO'
keywords: ['keyword1', 'keyword2']
openGraph:
  title: 'Titolo Open Graph'
  description: 'Descrizione Open Graph'
  image: '/blog-images/og-image.jpg'
schema:
  "@context": "https://schema.org"
  "@type": "Article"
  "headline": "Titolo dell'articolo"
  # ... altri metadati schema.org
---

Contenuto dell'articolo convertito in Markdown...
```

## 🔧 Funzionalità Avanzate

### Backup automatico
I file esistenti vengono automaticamente salvati in backup prima della sovrascrittura:
```
backups/blog/2024-01-15T10-30-00-000Z-articolo-esistente.md
```

### Dry Run (Simulazione)
Testa le modifiche senza scrivere file:
```bash
NODE_ENV=development node scripts/strapi-sync.js sync
```
(modifica `DEV_CONFIG.dryRun = true` in `strapi-config.js`)

### Modalità verbose
Abilita log dettagliati:
```bash
NODE_ENV=development node scripts/strapi-sync.js sync
```

### Gestione immagini
- **URL remoti**: Le immagini rimangono su Strapi
- **Download locale**: Le immagini vengono scaricate in `public/blog-images/`

## 🐛 Troubleshooting

### Errore: "STRAPI_API_TOKEN non impostato"
- Verifica di aver impostato correttamente la variabile d'ambiente
- Controlla che il token sia valido e non scaduto

### Errore: "Errore HTTP: 401"
- Il token API non è valido o è scaduto
- Verifica i permessi del token in Strapi

### Errore: "Errore HTTP: 403"
- Il token non ha i permessi per accedere agli articoli
- Verifica le impostazioni del token in Strapi

### Errore: "Nessun dato ricevuto da Strapi"
- Verifica che il content-type `articolos` esista in Strapi
- Controlla che ci siano articoli pubblicati

### Articoli non convertiti correttamente
- Verifica la struttura del rich text in Strapi
- Controlla i log per errori specifici di conversione

## 📝 Esempi d'uso

### Sincronizzazione quotidiana automatica
Aggiungi al tuo `package.json`:
```json
{
  "scripts": {
    "sync-blog": "node scripts/strapi-sync.js sync",
    "list-articles": "node scripts/strapi-sync.js list"
  }
}
```

Poi esegui:
```bash
npm run sync-blog
```

### Integrazione con CI/CD
```yaml
# .github/workflows/sync-blog.yml
name: Sync Blog from Strapi
on:
  schedule:
    - cron: '0 6 * * *'  # Ogni giorno alle 6:00
  workflow_dispatch:

jobs:
  sync:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: node scripts/strapi-sync.js sync
        env:
          STRAPI_API_TOKEN: ${{ secrets.STRAPI_API_TOKEN }}
      - uses: stefanzweifel/git-auto-commit-action@v4
        with:
          commit_message: 'Auto-sync blog articles from Strapi'
```

## 🔄 Aggiornamenti Schema Astro

Se modifichi la struttura degli articoli in Strapi, aggiorna anche `src/content/config.ts`:

```typescript
const blog = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    description: z.string(),
    pubDate: z.coerce.date(),
    heroImage: z.string().optional(),
    tags: z.array(z.string()).default([]),
    author: z.string().default('Marco Biagiotti'),
    category: z.string().optional(),
    seoTitle: z.string().optional(),
    seoDescription: z.string().optional(),
    keywords: z.array(z.string()).default([]),
    openGraph: z.object({
      title: z.string().optional(),
      description: z.string().optional(),
      image: z.string().optional(),
    }).optional(),
    schema: z.any().optional(),
  }),
});
```

## 🆘 Supporto

Per problemi o domande:
1. Controlla la sezione Troubleshooting
2. Verifica i log dettagliati con modalità verbose
3. Testa con un singolo articolo prima della sincronizzazione completa
