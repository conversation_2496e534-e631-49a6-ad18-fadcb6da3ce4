---
interface Props {
  repo: string;
  theme?: 'github-light' | 'github-dark' | 'preferred-color-scheme' | 'github-dark-orange' | 'icy-dark' | 'dark-blue' | 'photon-dark';
  issueTerm?: 'pathname' | 'url' | 'title' | 'og:title';
  label?: string;
}

const {
  repo = 'mbiagiottime/biagiotti-blog', // Your GitHub repo
  theme = 'github-light',
  issueTerm = 'pathname',
  label = 'comment'
} = Astro.props;
---

<div class="comments-section">
  <h3>💬 Commenti</h3>
  <p class="comments-info">
    I commenti sono gestiti tramite GitHub Discussions. 
    Effettua il login con GitHub per partecipare alla discussione.
  </p>
  
  <div id="utterances-container">
    <!-- Utterances will be loaded here -->
  </div>
</div>

<script define:vars={{ repo, theme, issueTerm, label }}>
  // Load Utterances comments
  function loadComments() {
    const container = document.getElementById('utterances-container');
    
    if (!container) return;
    
    // Clear any existing content
    container.innerHTML = '';
    
    const script = document.createElement('script');
    script.src = 'https://utteranc.es/client.js';
    script.setAttribute('repo', repo);
    script.setAttribute('issue-term', issueTerm);
    script.setAttribute('label', label);
    script.setAttribute('theme', theme);
    script.setAttribute('crossorigin', 'anonymous');
    script.async = true;
    
    container.appendChild(script);
  }

  // Load comments when the page loads
  document.addEventListener('DOMContentLoaded', loadComments);
  
  // Reload comments if the theme changes (for future theme switching functionality)
  window.addEventListener('theme-change', loadComments);
</script>

<style>
  .comments-section {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid rgb(var(--gray-light));
  }

  .comments-section h3 {
    margin-bottom: 1rem;
    color: rgb(var(--black));
    font-size: 1.5rem;
  }

  .comments-info {
    margin-bottom: 1.5rem;
    color: rgb(var(--gray));
    font-size: 0.9rem;
    background: #f8fafc;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid var(--accent);
  }

  #utterances-container {
    min-height: 200px;
  }

  /* Style the utterances iframe when it loads */
  :global(.utterances) {
    max-width: 100% !important;
  }

  @media (max-width: 768px) {
    .comments-section {
      margin-top: 2rem;
      padding-top: 1.5rem;
    }

    .comments-section h3 {
      font-size: 1.3rem;
    }
  }
</style>
