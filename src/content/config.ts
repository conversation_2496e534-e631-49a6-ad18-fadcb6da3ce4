import { defineCollection, z } from 'astro:content';

const blog = defineCollection({
	type: 'content',
	// Type-check frontmatter using a schema
	schema: z.object({
		title: z.string(),
		description: z.string(),
		// Transform string to Date object
		pubDate: z.coerce.date(),
		updatedDate: z.coerce.date().optional(),
		heroImage: z.string().optional(),
		// New fields for categorization
		category: z.string().optional(),
		tags: z.array(z.string()).default([]),
		// SEO and social fields
		author: z.string().default('<PERSON>'),
		readingTime: z.number().optional(),
		featured: z.boolean().default(false),
		// Publication control
		draft: z.boolean().default(false),
		published: z.boolean().default(true),
		publishDate: z.coerce.date().optional(),
		scheduledDate: z.coerce.date().optional(),
		autoPublish: z.boolean().default(false),
		// Extended SEO fields from Strapi
		seoTitle: z.string().optional(),
		seoDescription: z.string().optional(),
		keywords: z.array(z.string()).default([]),
		robots: z.string().optional(),
		canonical: z.string().optional(),
		// Open Graph fields
		openGraph: z.object({
			title: z.string().optional(),
			description: z.string().optional(),
			image: z.string().optional(),
			url: z.string().optional(),
			type: z.string().optional(),
		}).optional(),
		// Schema.org structured data
		schema: z.any().optional(),
		structuredData: z.any().optional(),
	}),
});

export const collections = { blog };
