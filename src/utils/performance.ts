// Performance optimization utilities

export class PerformanceOptimizer {
  // Lazy loading for images
  static setupLazyLoading() {
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            img.src = img.dataset.src || '';
            img.classList.remove('lazy');
            observer.unobserve(img);
          }
        });
      });

      document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
      });
    }
  }

  // Preload critical resources
  static preloadCriticalResources() {
    const criticalResources = [
      '/fonts/atkinson-regular.woff2',
      '/fonts/atkinson-bold.woff2'
    ];

    criticalResources.forEach(resource => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = resource;
      link.as = resource.includes('font') ? 'font' : 'image';
      if (resource.includes('font')) {
        link.crossOrigin = 'anonymous';
      }
      document.head.appendChild(link);
    });
  }

  // Debounce function for search and scroll events
  static debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  }

  // Throttle function for scroll events
  static throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  // Optimize scroll performance
  static optimizeScrollPerformance() {
    let ticking = false;

    const updateScrollPosition = () => {
      // Update scroll-dependent elements
      const scrolled = window.pageYOffset;
      const rate = scrolled * -0.5;
      
      // Parallax effects (if any)
      const parallaxElements = document.querySelectorAll('.parallax');
      parallaxElements.forEach(element => {
        (element as HTMLElement).style.transform = `translateY(${rate}px)`;
      });

      ticking = false;
    };

    const requestScrollUpdate = () => {
      if (!ticking) {
        requestAnimationFrame(updateScrollPosition);
        ticking = true;
      }
    };

    window.addEventListener('scroll', requestScrollUpdate, { passive: true });
  }

  // Prefetch next page content
  static setupPrefetching() {
    const prefetchLinks = document.querySelectorAll('a[data-prefetch]');
    
    if ('IntersectionObserver' in window) {
      const linkObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const link = entry.target as HTMLAnchorElement;
            this.prefetchPage(link.href);
            linkObserver.unobserve(link);
          }
        });
      });

      prefetchLinks.forEach(link => linkObserver.observe(link));
    }
  }

  static async prefetchPage(url: string) {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      if (response.ok) {
        // Create prefetch link
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = url;
        document.head.appendChild(link);
      }
    } catch (error) {
      console.warn('Prefetch failed:', error);
    }
  }

  // Critical CSS inlining
  static inlineCriticalCSS() {
    const criticalCSS = `
      /* Critical above-the-fold styles */
      body { margin: 0; font-family: 'Atkinson', sans-serif; }
      .header { background: white; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
      .hero { min-height: 50vh; display: flex; align-items: center; }
      .loading { opacity: 0; transition: opacity 0.3s ease; }
      .loaded { opacity: 1; }
    `;

    const style = document.createElement('style');
    style.textContent = criticalCSS;
    document.head.insertBefore(style, document.head.firstChild);
  }

  // Resource hints
  static addResourceHints() {
    const hints = [
      { rel: 'dns-prefetch', href: '//fonts.googleapis.com' },
      { rel: 'dns-prefetch', href: '//www.google-analytics.com' },
      { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: true },
    ];

    hints.forEach(hint => {
      const link = document.createElement('link');
      link.rel = hint.rel;
      link.href = hint.href;
      if (hint.crossorigin) {
        link.crossOrigin = 'anonymous';
      }
      document.head.appendChild(link);
    });
  }

  // Memory management
  static setupMemoryManagement() {
    // Clean up event listeners on page unload
    window.addEventListener('beforeunload', () => {
      // Remove all custom event listeners
      document.removeEventListener('scroll', this.optimizeScrollPerformance);
      
      // Clear any intervals/timeouts
      const highestTimeoutId = setTimeout(() => {}, 0);
      for (let i = 0; i < highestTimeoutId; i++) {
        clearTimeout(i);
      }
    });

    // Monitor memory usage (if available)
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory;
        if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
          console.warn('High memory usage detected');
          // Trigger garbage collection if possible
          if ('gc' in window) {
            (window as any).gc();
          }
        }
      }, 30000); // Check every 30 seconds
    }
  }

  // Initialize all optimizations
  static init() {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.setupOptimizations();
      });
    } else {
      this.setupOptimizations();
    }
  }

  private static setupOptimizations() {
    this.preloadCriticalResources();
    this.setupLazyLoading();
    this.optimizeScrollPerformance();
    this.setupPrefetching();
    this.addResourceHints();
    this.setupMemoryManagement();

    // Mark page as loaded
    document.body.classList.add('loaded');
  }
}

// Web Vitals monitoring
export class WebVitalsMonitor {
  static async init() {
    try {
      const { getCLS, getFID, getFCP, getLCP, getTTFB } = await import('web-vitals');

      getCLS(this.sendToAnalytics);
      getFID(this.sendToAnalytics);
      getFCP(this.sendToAnalytics);
      getLCP(this.sendToAnalytics);
      getTTFB(this.sendToAnalytics);
    } catch (error) {
      console.warn('Web Vitals not available:', error);
    }
  }

  private static sendToAnalytics(metric: any) {
    // Send to your analytics service
    if (window.gtag) {
      window.gtag('event', metric.name, {
        event_category: 'Web Vitals',
        event_label: metric.id,
        value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
        non_interaction: true,
      });
    }

    // Also send to custom analytics
    fetch('/api/analytics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        event_type: 'web_vital',
        metric_name: metric.name,
        metric_value: metric.value,
        metric_id: metric.id,
        timestamp: new Date().toISOString()
      })
    }).catch(error => console.warn('Analytics error:', error));
  }
}

// Bundle analyzer for development
export class BundleAnalyzer {
  static analyzeBundle() {
    if (process.env.NODE_ENV === 'development') {
      console.group('📦 Bundle Analysis');
      
      // Analyze loaded scripts
      const scripts = Array.from(document.scripts);
      const totalSize = scripts.reduce((size, script) => {
        return size + (script.src ? script.innerHTML.length : 0);
      }, 0);

      console.log(`Total scripts: ${scripts.length}`);
      console.log(`Estimated size: ${(totalSize / 1024).toFixed(2)} KB`);

      // Analyze CSS
      const stylesheets = Array.from(document.styleSheets);
      console.log(`Total stylesheets: ${stylesheets.length}`);

      // Check for unused CSS (basic check)
      const unusedSelectors: string[] = [];
      stylesheets.forEach(sheet => {
        try {
          const rules = Array.from(sheet.cssRules || []);
          rules.forEach(rule => {
            if (rule instanceof CSSStyleRule) {
              if (!document.querySelector(rule.selectorText)) {
                unusedSelectors.push(rule.selectorText);
              }
            }
          });
        } catch (error) {
          // Cross-origin stylesheets can't be analyzed
        }
      });

      if (unusedSelectors.length > 0) {
        console.warn(`Potentially unused selectors: ${unusedSelectors.length}`);
        console.log(unusedSelectors.slice(0, 10)); // Show first 10
      }

      console.groupEnd();
    }
  }
}

// Auto-initialize performance optimizations
if (typeof window !== 'undefined') {
  PerformanceOptimizer.init();
  WebVitalsMonitor.init();
  
  if (process.env.NODE_ENV === 'development') {
    BundleAnalyzer.analyzeBundle();
  }
}
