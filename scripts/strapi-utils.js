// scripts/strapi-utils.js
import fs from 'fs/promises';
import path from 'path';

// Placeholder implementations for Strapi utility classes
// These classes and methods will need to be implemented based on your specific requirements.

export class ImageManager {
  constructor(baseUrl, localImageDir) {
    this.baseUrl = baseUrl;
    this.localImageDir = localImageDir;
    console.log(`ImageManager initialized. Base URL: ${baseUrl}, Local Image Dir: ${localImageDir}`);
  }

  /**
   * Processes an image from Strapi.
   * This might involve downloading it, optimizing it, or returning its URL.
   * @param {object} imageData - The image data object from Strapi (e.g., article.attributes.Immagine).
   * @param {boolean} downloadImages - Whether to download the image locally.
   * @returns {Promise<string>} - The path or URL to the processed image.
   */
  async processImage(imageData, downloadImages) {
    console.log('[ImageManager.processImage] Called with:', imageData, `Download: ${downloadImages}`);
    if (!imageData || !imageData.data || !imageData.data.attributes || !imageData.data.attributes.url) {
      console.warn('[ImageManager.processImage] Invalid image data or URL missing.');
      return ''; // Return an empty string or a placeholder image path
    }

    const imageUrl = imageData.data.attributes.url;
    const imageName = path.basename(imageUrl);

    if (downloadImages) {
      // Ensure the local image directory exists
      await fs.mkdir(this.localImageDir, { recursive: true });
      const localPath = path.join(this.localImageDir, imageName);

      // Placeholder for actual image download logic
      console.log(`[ImageManager.processImage] Downloading ${this.baseUrl}${imageUrl} to ${localPath}`);
      // Simulating download: in a real scenario, you'd fetch and save the image.
      // For now, we'll just return the intended local path.
      // await fetchAndSaveImage(`${this.baseUrl}${imageUrl}`, localPath);
      return `/${path.relative(path.join(__dirname, '..', 'public'), localPath).replace(/\\/g, '/')}`; // Relative path for web
    } else {
      // Return the full URL if not downloading
      return `${this.baseUrl}${imageUrl}`;
    }
  }
}

export class SEOManager {
  /**
   * Generates SEO metadata.
   * @param {object} seoData - Strapi SEO component data.
   * @param {object} openGraphData - Strapi OpenGraph component data.
   * @param {object} article - The full article object.
   * @returns {object} - An object containing SEO meta tags.
   */
  static generateSEOMetadata(seoData, openGraphData, article) {
    console.log('[SEOManager.generateSEOMetadata] Called with:', seoData, openGraphData, article.attributes.Titolo);
    const metadata = {};
    if (seoData) {
      if (seoData.metaTitle) metadata.metaTitle = seoData.metaTitle;
      if (seoData.metaDescription) metadata.metaDescription = seoData.metaDescription;
      if (seoData.keywords) metadata.keywords = seoData.keywords.split(',').map(k => k.trim());
      // Add more SEO fields as needed
    }
    if (openGraphData) {
      if (openGraphData.title) metadata.ogTitle = openGraphData.title;
      if (openGraphData.description) metadata.ogDescription = openGraphData.description;
      // Add more OpenGraph fields as needed
    }
    return metadata;
  }

  /**
   * Generates Article Schema.org markup.
   * @param {object} article - The full article object.
   * @param {string} baseUrl - The base URL of the website.
   * @returns {object} - An object representing the Article schema.
   */
  static generateArticleSchema(article, baseUrl) {
    console.log('[SEOManager.generateArticleSchema] Called for:', article.attributes.Titolo);
    return {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": article.attributes.Titolo || '',
      "description": article.attributes.Sommario || article.attributes.seo?.metaDescription || '',
      // "image": article.attributes.Immagine?.data?.attributes?.url ? `${baseUrl}${article.attributes.Immagine.data.attributes.url}` : '',
      "author": {
        "@type": "Person",
        "name": article.attributes.Autore?.data?.attributes?.Nome || "Default Author"
      },
      "publisher": {
        "@type": "Organization",
        "name": "Your Site Name",
        // "logo": {
        //   "@type": "ImageObject",
        //   "url": `${baseUrl}/logo.png`
        // }
      },
      "datePublished": article.attributes.Data ? new Date(article.attributes.Data).toISOString() : new Date().toISOString(),
      "dateModified": article.attributes.updatedAt ? new Date(article.attributes.updatedAt).toISOString() : new Date().toISOString(),
      // Add more schema properties as needed
    };
  }
}

export class RichTextConverter {
  /**
   * Converts Strapi's rich text (blocks or markdown) to HTML or Markdown for Astro.
   * @param {object[]|string} richTextData - The rich text content from Strapi.
   * @returns {string} - The converted content as a string.
   */
  static convertToMarkdown(richTextData) {
    console.log('[RichTextConverter.convertToMarkdown] Called with:', richTextData);
    if (!richTextData) return '';
    // This is a very basic placeholder.
    // You'll need a proper library or custom logic to convert Strapi's rich text (blocks)
    // or if it's already markdown, you might just return it.
    if (typeof richTextData === 'string') {
      return richTextData; // Assuming it's already markdown
    }
    if (Array.isArray(richTextData)) {
      // Placeholder for Strapi blocks to markdown conversion
      return richTextData.map(block => {
        if (block.type === 'paragraph') {
          return block.children.map(child => child.text).join('');
        }
        // Add more block type handlers
        return '';
      }).join('\n\n');
    }
    return '';
  }
}

export class FileManager {
  /**
   * Generates a safe filename for the article.
   * @param {string} title - The article title.
   * @param {string|number} id - The article ID.
   * @returns {string} - A safe filename (e.g., 'my-article-title-123.md').
   */
  static generateSafeFilename(title, id) {
    const safeTitle = title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');
    return `${safeTitle}-${id}.md`;
  }

  /**
   * Checks if a file exists.
   * @param {string} filePath - The path to the file.
   * @returns {Promise<boolean>} - True if the file exists, false otherwise.
   */
  static async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Backs up an existing file.
   * @param {string} filePath - The path to the file to backup.
   * @param {string} backupDir - The directory to store the backup.
   * @returns {Promise<string|null>} - The path to the backup file, or null if failed.
   */
  static async backupFile(filePath, backupDir) {
    try {
      await fs.mkdir(backupDir, { recursive: true });
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = path.join(backupDir, `${path.basename(filePath, path.extname(filePath))}-${timestamp}${path.extname(filePath)}`);
      await fs.copyFile(filePath, backupPath);
      return backupPath;
    } catch (error) {
      console.error(`[FileManager.backupFile] Failed to backup ${filePath}:`, error);
      return null;
    }
  }
}

export class Validator {
  /**
   * Validates the configuration object.
   * @param {object} config - The configuration object from strapi-config.js.
   * @returns {string[]} - An array of error messages. Empty if valid.
   */
  static validateConfig(config) {
    const errors = [];
    if (!config.baseUrl) errors.push('Config error: baseUrl is missing.');
    if (!config.articlesEndpoint) errors.push('Config error: articlesEndpoint is missing.');
    if (!config.output || !config.output.blogDir) errors.push('Config error: output.blogDir is missing.');
    // Add more validation rules as needed
    if (errors.length > 0) {
        console.warn('[Validator.validateConfig] Validation errors:', errors)
    } else {
        console.log('[Validator.validateConfig] Config validated successfully.')
    }
    return errors;
  }

  /**
   * Validates an article object from Strapi.
   * @param {object} article - The article data from Strapi.
   * @returns {string[]} - An array of error messages. Empty if valid.
   */
  static validateArticle(article) {
    const errors = [];
    if (!article || !article.attributes) {
      errors.push('Article error: Invalid article structure.');
      return errors;
    }
    if (!article.attributes.Titolo) errors.push('Article error: Titolo (title) is missing.');
    if (!article.attributes.Contenuto) errors.push('Article error: Contenuto (content) is missing.');
    // Add more validation rules as needed
    if (errors.length > 0) {
        console.warn('[Validator.validateArticle] Validation errors for article ID', article.id, errors)
    }
    return errors;
  }
}

console.log('Strapi Utils loaded. Placeholder implementations active.');
