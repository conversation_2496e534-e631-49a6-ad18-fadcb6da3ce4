---
interface Props {
  postId: string;
  postTitle: string;
}

const { postId, postTitle } = Astro.props;
---

<div class="post-rating" data-post-id={postId}>
  <div class="rating-section">
    <h4>Ti è piaciuto questo articolo?</h4>
    <div class="rating-buttons">
      <button class="rating-btn" data-rating="5" aria-label="Eccellente">
        <span class="emoji">🤩</span>
        <span class="label">Eccellente</span>
      </button>
      <button class="rating-btn" data-rating="4" aria-label="Buono">
        <span class="emoji">😊</span>
        <span class="label">Buono</span>
      </button>
      <button class="rating-btn" data-rating="3" aria-label="Discreto">
        <span class="emoji">😐</span>
        <span class="label">Discreto</span>
      </button>
      <button class="rating-btn" data-rating="2" aria-label="Scarso">
        <span class="emoji">😕</span>
        <span class="label">Scarso</span>
      </button>
      <button class="rating-btn" data-rating="1" aria-label="Pessimo">
        <span class="emoji">😞</span>
        <span class="label">Pessimo</span>
      </button>
    </div>
  </div>

  <div class="feedback-section" style="display: none;">
    <h4>Grazie per il tuo feedback!</h4>
    <p>Vuoi dirci qualcosa in più?</p>
    <form class="feedback-form">
      <textarea 
        placeholder="Il tuo feedback (opzionale)..."
        rows="3"
        maxlength="500"
      ></textarea>
      <div class="feedback-actions">
        <button type="submit" class="submit-feedback">Invia Feedback</button>
        <button type="button" class="skip-feedback">Salta</button>
      </div>
    </form>
  </div>

  <div class="thank-you-section" style="display: none;">
    <div class="thank-you-content">
      <span class="thank-you-emoji">🙏</span>
      <h4>Grazie per il tuo feedback!</h4>
      <p>Il tuo parere ci aiuta a migliorare i contenuti.</p>
    </div>
  </div>

  <div class="rating-stats" style="display: none;">
    <div class="stats-summary">
      <span class="average-rating">0.0</span>
      <div class="stars"></div>
      <span class="total-ratings">(0 valutazioni)</span>
    </div>
  </div>
</div>

<script>
  class PostRating {
    constructor(container) {
      this.container = container;
      this.postId = container.dataset.postId;
      this.selectedRating = null;
      this.init();
    }

    init() {
      this.bindEvents();
      this.loadExistingRating();
      this.loadRatingStats();
    }

    bindEvents() {
      // Rating buttons
      const ratingBtns = this.container.querySelectorAll('.rating-btn');
      ratingBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
          const rating = parseInt(e.currentTarget.dataset.rating);
          this.submitRating(rating);
        });
      });

      // Feedback form
      const feedbackForm = this.container.querySelector('.feedback-form');
      feedbackForm?.addEventListener('submit', (e) => {
        e.preventDefault();
        this.submitFeedback();
      });

      const skipBtn = this.container.querySelector('.skip-feedback');
      skipBtn?.addEventListener('click', () => {
        this.showThankYou();
      });
    }

    async submitRating(rating) {
      this.selectedRating = rating;
      
      try {
        const response = await fetch('/api/ratings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            postId: this.postId,
            rating: rating,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
          })
        });

        if (response.ok) {
          this.showFeedbackSection();
          this.updateRatingStats();
        } else {
          throw new Error('Failed to submit rating');
        }
      } catch (error) {
        console.error('Rating submission error:', error);
        this.showError('Errore durante l\'invio della valutazione');
      }
    }

    async submitFeedback() {
      const textarea = this.container.querySelector('textarea');
      const feedback = textarea.value.trim();

      try {
        const response = await fetch('/api/feedback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            postId: this.postId,
            rating: this.selectedRating,
            feedback: feedback,
            timestamp: new Date().toISOString()
          })
        });

        if (response.ok) {
          this.showThankYou();
        } else {
          throw new Error('Failed to submit feedback');
        }
      } catch (error) {
        console.error('Feedback submission error:', error);
        this.showError('Errore durante l\'invio del feedback');
      }
    }

    showFeedbackSection() {
      this.container.querySelector('.rating-section').style.display = 'none';
      this.container.querySelector('.feedback-section').style.display = 'block';
    }

    showThankYou() {
      this.container.querySelector('.feedback-section').style.display = 'none';
      this.container.querySelector('.thank-you-section').style.display = 'block';
      
      // Store that user has rated this post
      localStorage.setItem(`rated-${this.postId}`, 'true');
    }

    async loadExistingRating() {
      // Check if user has already rated this post
      const hasRated = localStorage.getItem(`rated-${this.postId}`);
      if (hasRated) {
        this.container.querySelector('.rating-section').style.display = 'none';
        this.container.querySelector('.rating-stats').style.display = 'block';
      }
    }

    async loadRatingStats() {
      try {
        const response = await fetch(`/api/ratings/${this.postId}`);
        if (response.ok) {
          const stats = await response.json();
          this.displayStats(stats);
        }
      } catch (error) {
        console.error('Error loading rating stats:', error);
      }
    }

    displayStats(stats) {
      const statsContainer = this.container.querySelector('.rating-stats');
      const averageEl = statsContainer.querySelector('.average-rating');
      const starsEl = statsContainer.querySelector('.stars');
      const totalEl = statsContainer.querySelector('.total-ratings');

      averageEl.textContent = stats.average.toFixed(1);
      totalEl.textContent = `(${stats.total} valutazioni)`;
      
      // Generate stars
      starsEl.innerHTML = this.generateStars(stats.average);
    }

    generateStars(rating) {
      const fullStars = Math.floor(rating);
      const hasHalfStar = rating % 1 >= 0.5;
      const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

      let stars = '';
      
      // Full stars
      for (let i = 0; i < fullStars; i++) {
        stars += '<span class="star full">★</span>';
      }
      
      // Half star
      if (hasHalfStar) {
        stars += '<span class="star half">★</span>';
      }
      
      // Empty stars
      for (let i = 0; i < emptyStars; i++) {
        stars += '<span class="star empty">☆</span>';
      }

      return stars;
    }

    async updateRatingStats() {
      // Refresh stats after rating
      setTimeout(() => {
        this.loadRatingStats();
      }, 1000);
    }

    showError(message) {
      const errorDiv = document.createElement('div');
      errorDiv.className = 'rating-error';
      errorDiv.textContent = message;
      this.container.appendChild(errorDiv);
      
      setTimeout(() => {
        errorDiv.remove();
      }, 3000);
    }
  }

  // Initialize rating components
  document.addEventListener('DOMContentLoaded', () => {
    const ratingContainers = document.querySelectorAll('.post-rating');
    ratingContainers.forEach(container => {
      new PostRating(container);
    });
  });
</script>

<style>
  .post-rating {
    background: #f8fafc;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 2rem 0;
    border: 1px solid #e2e8f0;
  }

  .rating-section h4,
  .feedback-section h4,
  .thank-you-section h4 {
    margin: 0 0 1rem 0;
    color: rgb(var(--black));
    font-size: 1.1rem;
  }

  .rating-buttons {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    justify-content: center;
  }

  .rating-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 0.75rem;
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
  }

  .rating-btn:hover {
    border-color: var(--accent);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .rating-btn .emoji {
    font-size: 1.5rem;
  }

  .rating-btn .label {
    font-size: 0.8rem;
    color: rgb(var(--gray));
    font-weight: 500;
  }

  .feedback-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .feedback-form textarea {
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-family: inherit;
    font-size: 0.9rem;
    resize: vertical;
  }

  .feedback-form textarea:focus {
    outline: none;
    border-color: var(--accent);
  }

  .feedback-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
  }

  .submit-feedback,
  .skip-feedback {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .submit-feedback {
    background: var(--accent);
    color: white;
    border: none;
  }

  .submit-feedback:hover {
    background: rgb(var(--accent-dark, var(--accent)));
  }

  .skip-feedback {
    background: #f1f5f9;
    color: rgb(var(--gray));
    border: 1px solid #e2e8f0;
  }

  .skip-feedback:hover {
    background: #e2e8f0;
  }

  .thank-you-content {
    text-align: center;
  }

  .thank-you-emoji {
    font-size: 2rem;
    display: block;
    margin-bottom: 0.5rem;
  }

  .thank-you-content p {
    margin: 0.5rem 0 0 0;
    color: rgb(var(--gray));
  }

  .rating-stats {
    text-align: center;
  }

  .stats-summary {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .average-rating {
    font-size: 1.2rem;
    font-weight: bold;
    color: rgb(var(--black));
  }

  .stars {
    display: flex;
    gap: 0.1rem;
  }

  .star {
    font-size: 1rem;
  }

  .star.full {
    color: #fbbf24;
  }

  .star.half {
    color: #fbbf24;
    opacity: 0.5;
  }

  .star.empty {
    color: #d1d5db;
  }

  .total-ratings {
    font-size: 0.875rem;
    color: rgb(var(--gray));
  }

  .rating-error {
    background: #fef2f2;
    color: #dc2626;
    padding: 0.75rem;
    border-radius: 6px;
    margin-top: 1rem;
    font-size: 0.875rem;
  }

  @media (max-width: 768px) {
    .rating-buttons {
      gap: 0.5rem;
    }

    .rating-btn {
      min-width: 60px;
      padding: 0.75rem 0.5rem;
    }

    .rating-btn .emoji {
      font-size: 1.2rem;
    }

    .rating-btn .label {
      font-size: 0.7rem;
    }

    .feedback-actions {
      flex-direction: column;
    }
  }
</style>
