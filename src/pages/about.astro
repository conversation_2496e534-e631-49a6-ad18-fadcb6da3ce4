---
import Layout from '../layouts/Layout.astro';
---

<Layout title="About - Marco <PERSON> Blog" description="Scopri di più su <PERSON> e il suo blog">
	<main>
		<div class="about-container">
			<h1>Chi Sono</h1>
			
			<div class="about-content">
				<div class="about-text">
					<p>
						Ciao! Sono <PERSON>, uno sviluppatore appassionato di tecnologia e innovazione.
					</p>
					
					<p>
						In questo blog condivido le mie esperienze, tutorial e riflessioni su:
					</p>
					
					<ul>
						<li>🚀 Sviluppo Web e Mobile</li>
						<li>🤖 Intelligenza Artificiale</li>
						<li>⚡ Performance e Ottimizzazione</li>
						<li>🛠️ Tools e Framework Moderni</li>
						<li>💡 Best Practices e Architetture</li>
					</ul>
					
					<p>
						L'obiettivo è creare contenuti utili e pratici per la community di sviluppatori.
					</p>
					
					<div class="contact-info">
						<h3>Con<PERSON>ti</h3>
						<p>📧 Email: <EMAIL></p>
						<p>🐦 Twitter: @marcobi</p>
						<p>💼 LinkedIn: <PERSON> Biagiotti</p>
						<p>🐙 GitHub: mbiagiottime</p>
					</div>
				</div>
				
				<div class="about-image">
					<div class="placeholder-avatar">
						<span>MB</span>
					</div>
				</div>
			</div>
		</div>
	</main>
</Layout>

<style>
	main {
		width: 960px;
		max-width: calc(100% - 2rem);
		margin: 0 auto;
		padding: 2rem 0;
	}

	.about-container {
		background: white;
		border-radius: 12px;
		padding: 2rem;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	}

	h1 {
		font-size: 2.5rem;
		text-align: center;
		margin-bottom: 2rem;
		color: rgb(var(--black));
	}

	.about-content {
		display: grid;
		grid-template-columns: 2fr 1fr;
		gap: 2rem;
		align-items: start;
	}

	.about-text {
		font-size: 1.1rem;
		line-height: 1.7;
		color: rgb(var(--gray));
	}

	.about-text p {
		margin-bottom: 1.5rem;
	}

	.about-text ul {
		margin: 1.5rem 0;
		padding-left: 1rem;
	}

	.about-text li {
		margin-bottom: 0.5rem;
	}

	.contact-info {
		background: #f8fafc;
		padding: 1.5rem;
		border-radius: 8px;
		margin-top: 2rem;
	}

	.contact-info h3 {
		margin-top: 0;
		margin-bottom: 1rem;
		color: rgb(var(--black));
	}

	.contact-info p {
		margin: 0.5rem 0;
		font-family: monospace;
		font-size: 0.9rem;
	}

	.about-image {
		display: flex;
		justify-content: center;
		align-items: flex-start;
	}

	.placeholder-avatar {
		width: 200px;
		height: 200px;
		border-radius: 50%;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		display: flex;
		align-items: center;
		justify-content: center;
		color: white;
		font-size: 3rem;
		font-weight: bold;
		box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
	}

	@media (max-width: 768px) {
		main {
			padding: 1rem 0;
		}

		.about-container {
			padding: 1.5rem;
		}

		.about-content {
			grid-template-columns: 1fr;
			gap: 1.5rem;
		}

		.about-image {
			order: -1;
		}

		.placeholder-avatar {
			width: 150px;
			height: 150px;
			font-size: 2rem;
		}

		h1 {
			font-size: 2rem;
		}
	}
</style>
