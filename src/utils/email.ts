import nodemailer from 'nodemailer';

// Email configuration
const emailConfig = {
  host: process.env.SMTP_HOST || 'localhost',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER || '<EMAIL>',
    pass: process.env.SMTP_PASSWORD || '',
  },
  from: process.env.SMTP_FROM || '<EMAIL>',
};

// Create transporter
let transporter: nodemailer.Transporter | null = null;

function getTransporter() {
  if (!transporter) {
    transporter = nodemailer.createTransporter(emailConfig);
  }
  return transporter;
}

// Email templates
export const emailTemplates = {
  contactForm: {
    subject: 'Nuovo messaggio dal sito web',
    html: (data: { name: string; email: string; subject?: string; message: string }) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Nuovo messaggio dal sito</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #00a99d; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .field { margin-bottom: 15px; }
          .label { font-weight: bold; color: #555; }
          .value { margin-top: 5px; }
          .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Nuovo messaggio dal sito web</h1>
          </div>
          <div class="content">
            <div class="field">
              <div class="label">Nome:</div>
              <div class="value">${data.name}</div>
            </div>
            <div class="field">
              <div class="label">Email:</div>
              <div class="value">${data.email}</div>
            </div>
            ${data.subject ? `
            <div class="field">
              <div class="label">Oggetto:</div>
              <div class="value">${data.subject}</div>
            </div>
            ` : ''}
            <div class="field">
              <div class="label">Messaggio:</div>
              <div class="value">${data.message.replace(/\n/g, '<br>')}</div>
            </div>
          </div>
          <div class="footer">
            <p>Messaggio ricevuto dal sito marcobiagiotti.me</p>
          </div>
        </div>
      </body>
      </html>
    `,
  },
  
  newsletterWelcome: {
    subject: 'Benvenuto nella newsletter di Marco Biagiotti',
    html: (data: { name?: string; email: string }) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Benvenuto nella newsletter</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #00a99d; color: white; padding: 30px; text-align: center; }
          .content { padding: 30px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
          .btn { display: inline-block; padding: 12px 24px; background: #00a99d; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Benvenuto nella newsletter!</h1>
          </div>
          <div class="content">
            <p>Ciao ${data.name || 'lettore'},</p>
            <p>Grazie per esserti iscritto alla newsletter di Marco Biagiotti!</p>
            <p>Riceverai aggiornamenti su:</p>
            <ul>
              <li>Nuovi articoli del blog</li>
              <li>Insights su innovazione e digital transformation</li>
              <li>Novità dal mondo automotive</li>
              <li>Eventi e conferenze</li>
            </ul>
            <p>Puoi visitare il blog per leggere gli ultimi articoli:</p>
            <a href="https://marcobiagiotti.me/blog" class="btn">Visita il Blog</a>
            <p>A presto!</p>
            <p><strong>Marco Biagiotti</strong><br>Innovation Manager</p>
          </div>
          <div class="footer">
            <p>Marco Biagiotti - P.IVA: 05337010481</p>
            <p>Email: <EMAIL></p>
          </div>
        </div>
      </body>
      </html>
    `,
  },
  
  contactConfirmation: {
    subject: 'Messaggio ricevuto - Ti risponderò presto',
    html: (data: { name: string; email: string }) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Messaggio ricevuto</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #00a99d; color: white; padding: 30px; text-align: center; }
          .content { padding: 30px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Messaggio ricevuto!</h1>
          </div>
          <div class="content">
            <p>Ciao ${data.name},</p>
            <p>Grazie per avermi contattato! Ho ricevuto il tuo messaggio e ti risponderò il prima possibile.</p>
            <p>Nel frattempo, puoi:</p>
            <ul>
              <li>Leggere gli ultimi articoli del mio blog</li>
              <li>Seguirmi sui social media</li>
              <li>Scoprire di più sui miei servizi</li>
            </ul>
            <p>A presto!</p>
            <p><strong>Marco Biagiotti</strong><br>Innovation Manager</p>
          </div>
          <div class="footer">
            <p>Marco Biagiotti - P.IVA: 05337010481</p>
            <p>Email: <EMAIL></p>
          </div>
        </div>
      </body>
      </html>
    `,
  },
};

// Send email function
export async function sendEmail(to: string, template: keyof typeof emailTemplates, data: any): Promise<boolean> {
  try {
    const transporter = getTransporter();
    const templateConfig = emailTemplates[template];
    
    const mailOptions = {
      from: `"Marco Biagiotti" <${emailConfig.from}>`,
      to: to,
      subject: templateConfig.subject,
      html: templateConfig.html(data),
    };
    
    await transporter.sendMail(mailOptions);
    console.log(`✅ Email sent successfully to ${to}`);
    return true;
  } catch (error) {
    console.error('❌ Error sending email:', error);
    return false;
  }
}

// Send contact form notification
export async function sendContactNotification(contactData: {
  name: string;
  email: string;
  subject?: string;
  message: string;
}): Promise<boolean> {
  try {
    const transporter = getTransporter();
    
    // Send notification to Marco
    const notificationOptions = {
      from: `"Sito Web" <${emailConfig.from}>`,
      to: '<EMAIL>',
      subject: emailTemplates.contactForm.subject,
      html: emailTemplates.contactForm.html(contactData),
      replyTo: contactData.email,
    };
    
    await transporter.sendMail(notificationOptions);
    
    // Send confirmation to user
    await sendEmail(contactData.email, 'contactConfirmation', contactData);
    
    return true;
  } catch (error) {
    console.error('❌ Error sending contact notification:', error);
    return false;
  }
}

// Send newsletter welcome email
export async function sendNewsletterWelcome(email: string, name?: string): Promise<boolean> {
  return await sendEmail(email, 'newsletterWelcome', { email, name });
}

// Test email configuration
export async function testEmailConfig(): Promise<boolean> {
  try {
    const transporter = getTransporter();
    await transporter.verify();
    console.log('✅ SMTP configuration is valid');
    return true;
  } catch (error) {
    console.error('❌ SMTP configuration error:', error);
    return false;
  }
}
