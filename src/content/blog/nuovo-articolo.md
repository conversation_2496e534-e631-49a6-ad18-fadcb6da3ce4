---
title: 'Nuovo Articolo di Esempio'
description: 'Un articolo di esempio che funziona correttamente'
pubDate: '2023-05-30'
heroImage: '/blog-placeholder-3.jpg'
category: 'Guide'
tags: ['Esempio', 'Blog', 'Astro']
author: '<PERSON>'
featured: false
---

# Nuovo Articolo di Esempio

Questo è un articolo di esempio che dovrebbe apparire nel blog correttamente.

## Ecco come funziona

Per far apparire correttamente gli articoli nel blog, è importante seguire questi punti:

1. Assicurarsi che il file sia nella cartella `src/content/blog/`
2. Il formato della data deve essere corretto (YYYY-MM-DD)
3. **Importante**: La data di pubblicazione non deve essere futura
4. Il frontmatter deve includere tutti i campi richiesti (title, description, pubDate)

## Immagini e formattazione

Puoi includere testo formattato come **grassetto**, *corsivo*, e [link](/).

Ecco un esempio di lista:
- Primo elemento
- Secondo elemento
- Terzo elemento

Questo articolo dovrebbe apparire correttamente nella pagina del blog!