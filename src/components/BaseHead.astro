---
// Import the global.css file here so that it is included on
// all pages through the use of the <BaseHead /> component.
import '../styles/global.css';

interface Props {
	title: string;
	description: string;
	image?: string;
	type?: 'website' | 'article';
	publishDate?: Date;
	modifiedDate?: Date;
	author?: string;
	category?: string;
	tags?: string[];
}

const canonicalURL = new URL(Astro.url.pathname, Astro.site);

const {
	title,
	description,
	image = '/blog-placeholder-1.jpg',
	type = 'website',
	publishDate,
	modifiedDate,
	author,
	category,
	tags = []
} = Astro.props;

const fullImageUrl = new URL(image, Astro.site).toString();
---

<!-- Global Metadata -->
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
<link rel="icon" type="image/x-icon" href="/favicon.ico">
<link rel="icon" type="image/png" sizes="192x192" href="/android-chrome-192x192.png">
<link rel="icon" type="image/png" sizes="512x512" href="/android-chrome-512x512.png">
<meta name="generator" content={Astro.generator} />

<!-- Font preloads -->
<link rel="preload" href="/fonts/atkinson-regular.woff" as="font" type="font/woff" crossorigin />
<link rel="preload" href="/fonts/atkinson-bold.woff" as="font" type="font/woff" crossorigin />

<!-- Canonical URL -->
<link rel="canonical" href={canonicalURL} />

<!-- Primary Meta Tags -->
<title>{title}</title>
<meta name="title" content={title} />
<meta name="description" content={description} />

<!-- Open Graph / Facebook -->
<meta property="og:type" content={type === 'article' ? 'article' : 'website'} />
<meta property="og:url" content={Astro.url} />
<meta property="og:title" content={title} />
<meta property="og:description" content={description} />
<meta property="og:image" content={fullImageUrl} />
<meta property="og:image:width" content="1200" />
<meta property="og:image:height" content="630" />
<meta property="og:site_name" content="Marco Biagiotti Blog" />
<meta property="og:locale" content="it_IT" />

<!-- Article specific Open Graph tags -->
{type === 'article' && (
	<>
		{author && <meta property="article:author" content={author} />}
		{publishDate && <meta property="article:published_time" content={publishDate.toISOString()} />}
		{modifiedDate && <meta property="article:modified_time" content={modifiedDate.toISOString()} />}
		{category && <meta property="article:section" content={category} />}
		{tags.map(tag => <meta property="article:tag" content={tag} />)}
	</>
)}

<!-- Twitter -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:site" content="@marcobi" />
<meta name="twitter:creator" content="@marcobi" />
<meta name="twitter:url" content={Astro.url} />
<meta name="twitter:title" content={title} />
<meta name="twitter:description" content={description} />
<meta name="twitter:image" content={fullImageUrl} />

<!-- Additional SEO meta tags -->
<meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
<meta name="googlebot" content="index, follow" />
{author && <meta name="author" content={author} />}
{category && <meta name="category" content={category} />}
{tags.length > 0 && <meta name="keywords" content={tags.join(', ')} />}

<!-- Language and region -->
<meta name="language" content="Italian" />
<meta name="geo.region" content="IT" />
<meta name="geo.country" content="Italy" />

<!-- Theme color for mobile browsers -->
<meta name="theme-color" content="#2337ff" />
<meta name="msapplication-TileColor" content="#2337ff" />
