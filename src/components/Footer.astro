---
const today = new Date();
---

<footer class="footer">
	<div class="container">
		<div class="footer-content">
			<div class="footer-info">
				<p class="copyright">
					© Copyright Marco <PERSON> Innovation Manager<br>
					P.IVA: 05337010481 - Email: <a href="mailto:<EMAIL>"><EMAIL></a>
				</p>
				<p class="privacy-info">
					Questa pagina non contiene cookies statistici né di profilazione |
					<button class="privacy-link" onclick="openPrivacyModal()">Privacy Policy</button>
				</p>
			</div>
		</div>
	</div>
</footer>

<!-- Back to Top Button -->
<button id="back-to-top" class="back-to-top" aria-label="Torna all'inizio">
	<i class="bi bi-arrow-up"></i>
</button>

<!-- Privacy Policy Modal -->
<div id="privacyModal" class="modal-overlay" onclick="closePrivacyModal()">
	<div class="modal-content" onclick="event.stopPropagation()">
		<button class="modal-close-x" onclick="closePrivacyModal()" aria-label="Chiudi Privacy Policy">
			<i class="bi bi-x-lg"></i>
		</button>
		<div class="modal-header">
			<h2>Privacy Policy</h2>
		</div>
		<div class="modal-body">
			<h3>1. Introduzione</h3>
			<p>La presente Informativa sulla Privacy descrive come Marco Biagiotti raccoglie, utilizza e condivide i dati personali quando utilizzi il nostro sito web o quando interagisci con noi per richiedere informazioni o servizi.</p>
			<p>Marco Biagiotti rispetta la tua privacy e si impegna a proteggere i tuoi dati personali. Questa informativa ti aiuterà a comprendere come trattiamo i tuoi dati e quali sono i tuoi diritti in materia di privacy.</p>

			<h3>2. Titolare del Trattamento</h3>
			<p>Il Titolare del trattamento dei dati è:</p>
			<p><strong>Marco Biagiotti</strong><br>
			50041 Calenzano (FI)<br>
			P.IVA: 05337010481<br>
			Email: <EMAIL></p>

			<h3>3. Dati Personali che Raccogliamo</h3>
			<p>Possiamo raccogliere, utilizzare, archiviare e trasferire diversi tipi di dati personali che ti riguardano, tra cui:</p>
			<ul>
				<li><strong>Dati identificativi:</strong> nome, cognome</li>
				<li><strong>Dati di contatto:</strong> indirizzo email, numero di telefono</li>
				<li><strong>Dati tecnici:</strong> indirizzo IP, tipo e versione del browser, impostazioni del fuso orario, tipi e versioni di plug-in del browser, sistema operativo e piattaforma</li>
				<li><strong>Dati di utilizzo:</strong> informazioni su come utilizzi il nostro sito web</li>
				<li><strong>Dati di marketing e comunicazione:</strong> le tue preferenze nel ricevere comunicazioni di marketing da noi</li>
			</ul>

			<h3>4. Come Raccogliamo i Tuoi Dati Personali</h3>
			<p>Raccogliamo i tuoi dati personali attraverso diversi metodi, tra cui:</p>
			<ul>
				<li><strong>Interazioni dirette:</strong> quando compili moduli sul nostro sito web, ci contatti via email o telefono</li>
				<li><strong>Tecnologie automatizzate:</strong> quando visiti il nostro sito web, possiamo raccogliere automaticamente dati tecnici sul tuo dispositivo e sulle tue azioni di navigazione</li>
				<li><strong>Terze parti:</strong> possiamo ricevere dati personali su di te da vari terzi, come fornitori di analisi o provider di servizi tecnici</li>
			</ul>

			<h3>5. Come Utilizziamo i Tuoi Dati Personali</h3>
			<p>Utilizziamo i tuoi dati personali solo quando la legge lo consente. Più comunemente, utilizzeremo i tuoi dati personali nelle seguenti circostanze:</p>
			<ul>
				<li>Per fornirti informazioni o servizi che hai richiesto</li>
				<li>Per adempiere a un contratto che stiamo per stipulare o che abbiamo stipulato con te</li>
				<li>Quando è necessario per i nostri legittimi interessi (o quelli di terzi) e i tuoi interessi e diritti fondamentali non prevalgono su tali interessi</li>
				<li>Quando dobbiamo adempiere a un obbligo legale o normativo</li>
			</ul>

			<h3>6. Conservazione dei Dati</h3>
			<p>Conserveremo i tuoi dati personali solo per il tempo necessario a soddisfare le finalità per cui li abbiamo raccolti, incluso il soddisfacimento di requisiti legali, contabili o di rendicontazione.</p>

			<h3>7. I Tuoi Diritti</h3>
			<p>In determinate circostanze, hai diritti ai sensi delle leggi sulla protezione dei dati in relazione ai tuoi dati personali, tra cui:</p>
			<ul>
				<li>Richiedere l'accesso ai tuoi dati personali</li>
				<li>Richiedere la correzione dei tuoi dati personali</li>
				<li>Richiedere la cancellazione dei tuoi dati personali</li>
				<li>Opporti al trattamento dei tuoi dati personali</li>
				<li>Richiedere la limitazione del trattamento dei tuoi dati personali</li>
				<li>Richiedere il trasferimento dei tuoi dati personali</li>
				<li>Diritto di revocare il consenso</li>
			</ul>

			<h3>8. Cookie</h3>
			<p>Il nostro sito web utilizza cookie per distinguerti dagli altri utenti. Questo ci aiuta a fornirti una buona esperienza quando navighi sul nostro sito e ci consente anche di migliorarlo.</p>

			<h3>9. Modifiche alla Privacy Policy</h3>
			<p>Possiamo aggiornare la nostra informativa sulla privacy di tanto in tanto. Eventuali modifiche saranno pubblicate su questa pagina.</p>

			<h3>10. Contatti</h3>
			<p>Per qualsiasi domanda riguardante questa informativa sulla privacy o le nostre pratiche in materia di dati, contattaci all'indirizzo:</p>
			<p><strong>Email:</strong> <EMAIL></p>
		</div>
	</div>
</div>
<style>
	.footer {
		background: #2d3748;
		color: #e2e8f0;
		padding: 30px 0;
		margin-top: 50px;
	}

	.footer-content {
		text-align: center;
	}

	.footer-info {
		max-width: 800px;
		margin: 0 auto;
	}

	.copyright {
		font-size: 14px;
		margin-bottom: 10px;
		line-height: 1.6;
	}

	.copyright a {
		color: var(--theme-color);
		text-decoration: none;
	}

	.copyright a:hover {
		text-decoration: underline;
	}

	.privacy-info {
		font-size: 12px;
		color: #a0aec0;
		margin: 0;
	}

	.privacy-link {
		background: none;
		border: none;
		color: var(--theme-color);
		text-decoration: underline;
		cursor: pointer;
		font-size: 12px;
		padding: 0;
	}

	.privacy-link:hover {
		color: #00c4b8;
	}

	/* Back to Top Button */
	.back-to-top {
		position: fixed;
		bottom: 30px;
		right: 30px;
		width: 50px;
		height: 50px;
		background: var(--theme-color);
		color: white;
		border: none;
		border-radius: 50%;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 1.2rem;
		box-shadow: 0 4px 15px rgba(var(--theme-color-rgb), 0.3);
		transition: all 0.3s ease;
		opacity: 0;
		visibility: hidden;
		transform: translateY(20px);
		z-index: 1000;
	}

	.back-to-top.visible {
		opacity: 1;
		visibility: visible;
		transform: translateY(0);
	}

	.back-to-top:hover {
		background: color-mix(in srgb, var(--theme-color), black 10%);
		transform: translateY(-2px);
		box-shadow: 0 8px 25px rgba(var(--theme-color-rgb), 0.4);
	}

	.back-to-top:active {
		transform: translateY(0);
	}

	/* Modal Styles */
	.modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.8);
		display: none;
		justify-content: center;
		align-items: center;
		z-index: 10000;
		padding: 20px;
		box-sizing: border-box;
	}

	.modal-overlay.active {
		display: flex;
	}

	.modal-content {
		background: white;
		border-radius: 12px;
		max-width: 800px;
		width: 100%;
		max-height: 90vh;
		overflow-y: auto;
		box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
		position: relative;
	}

	.modal-header {
		padding: 20px 30px;
		border-bottom: 1px solid #e2e8f0;
		background: #f7fafc;
		border-radius: 12px 12px 0 0;
	}

	.modal-header h2 {
		margin: 0;
		color: #2d3748;
		font-size: 1.5rem;
	}

	/* New X Close Button */
	.modal-close-x {
		position: absolute;
		top: 15px;
		right: 15px;
		background: rgba(255, 255, 255, 0.95);
		border: 2px solid #e2e8f0;
		width: 40px;
		height: 40px;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		font-size: 18px;
		color: #4a5568;
		transition: all 0.3s ease;
		z-index: 10001;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}

	.modal-close-x:hover {
		background: #ffffff;
		border-color: #cbd5e0;
		color: #2d3748;
		transform: scale(1.1);
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
	}

	.modal-close-x:active {
		transform: scale(0.95);
	}

	.modal-body {
		padding: 30px;
		color: #2d3748;
		line-height: 1.6;
	}

	.modal-body h3 {
		color: var(--theme-color);
		margin-top: 25px;
		margin-bottom: 15px;
		font-size: 1.2rem;
	}

	.modal-body h3:first-child {
		margin-top: 0;
	}

	.modal-body p {
		margin-bottom: 15px;
	}

	.modal-body ul {
		margin-bottom: 15px;
		padding-left: 20px;
	}

	.modal-body li {
		margin-bottom: 8px;
	}

	@media (max-width: 768px) {
		.modal-content {
			margin: 10px;
			max-height: 95vh;
		}

		.modal-header,
		.modal-body {
			padding: 20px;
		}

		.modal-header h2 {
			font-size: 1.3rem;
		}

		.modal-close-x {
			top: 10px;
			right: 10px;
			width: 35px;
			height: 35px;
			font-size: 16px;
		}

		.footer {
			padding: 20px 0;
		}

		.copyright {
			font-size: 13px;
		}

		.privacy-info {
			font-size: 11px;
		}

		.back-to-top {
			bottom: 20px;
			right: 20px;
			width: 45px;
			height: 45px;
			font-size: 1.1rem;
		}
	}
</style>

<script>
	// Make functions global for onclick handlers
	window.openPrivacyModal = function() {
		document.getElementById('privacyModal').classList.add('active');
		document.body.style.overflow = 'hidden';
	}

	window.closePrivacyModal = function() {
		document.getElementById('privacyModal').classList.remove('active');
		document.body.style.overflow = 'auto';
	}

	// Close modal with ESC key
	document.addEventListener('keydown', function(e) {
		if (e.key === 'Escape') {
			closePrivacyModal();
		}
	});

	// Back to Top functionality
	const backToTopBtn = document.getElementById('back-to-top');

	// Show/hide button based on scroll position
	window.addEventListener('scroll', function() {
		if (window.pageYOffset > 300) {
			backToTopBtn.classList.add('visible');
		} else {
			backToTopBtn.classList.remove('visible');
		}
	});

	// Scroll to top when clicked
	backToTopBtn.addEventListener('click', function() {
		window.scrollTo({
			top: 0,
			behavior: 'smooth'
		});
	});
</script>