# 🚀 Guida Installazione Completa

Questa guida ti accompagnerà nell'installazione completa del blog <PERSON> con tutte le funzionalità.

## 📋 Prerequisiti

### Software Richiesto
- **Node.js** 18+ ([Download](https://nodejs.org/))
- **npm** 9+ (incluso con Node.js)
- **MySQL** 5.7+ ([Download](https://dev.mysql.com/downloads/))
- **Git** ([Download](https://git-scm.com/))

### Conoscenze Consigliate
- Comandi base del terminale
- Configurazione database MySQL
- Gestione file di ambiente (.env)

### Account Opzionali
- **GitHub** - Per commenti Utterances e deploy
- **Vercel/Netlify** - Per hosting frontend
- **Google Analytics** - Per analytics
- **Server SMTP** - Per email automatiche

## 🔧 Installazione Passo-Passo

### 1. **Clone del Repository**

```bash
# Clona il repository
git clone https://github.com/mbiagiottime/biagiotti-blog.git

# Entra nella cartella
cd biagiotti-blog

# Verifica la struttura
ls -la
```

### 2. **Installazione Dipendenze**

```bash
# Installa tutte le dipendenze
npm install

# Verifica installazione
npm list --depth=0
```

**Dipendenze principali installate**:
- `astro` - Framework principale
- `@astrojs/node` - Adapter Node.js per output hybrid
- `mysql2` - Driver MySQL
- `nodemailer` - Invio email SMTP
- `@types/nodemailer` - Tipi TypeScript

**Architettura Hybrid**:
Il blog utilizza l'output `hybrid` di Astro che combina:
- **Static Generation** per pagine blog, articoli e contenuti
- **Server-Side Rendering** per API routes (newsletter, contatti)
- **Node.js Adapter** per deployment production-ready

### 3. **Configurazione Database MySQL**

#### A. **Avvia MySQL**

```bash
# Su macOS con Homebrew
brew services start mysql

# Su Ubuntu/Debian
sudo systemctl start mysql

# Su Windows
# Avvia MySQL dal pannello servizi
```

#### B. **Crea Database**

```bash
# Accedi a MySQL
mysql -u root -p

# Crea il database
CREATE DATABASE biagiotti_blog CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# Crea utente dedicato (raccomandato)
CREATE USER 'biagiotti_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON biagiotti_blog.* TO 'biagiotti_user'@'localhost';
FLUSH PRIVILEGES;

# Esci da MySQL
EXIT;
```

#### C. **Importa Schema**

```bash
# Dalla root del progetto
mysql -u biagiotti_user -p biagiotti_blog < database/schema.sql

# Verifica tabelle create
mysql -u biagiotti_user -p biagiotti_blog -e "SHOW TABLES;"
```

### 4. **Configurazione Environment**

#### A. **Crea File .env**

```bash
# Copia il template
cp .env.example .env

# Modifica con il tuo editor preferito
nano .env
# oppure
code .env
```

#### B. **Configura Variabili**

```env
# Strapi Configuration
STRAPI_BASE_URL=https://your_strapi_instance_url # Configured by the setup wizard or manually
# Strapi API Token (Required for fetching articles)
STRAPI_API_TOKEN=your_strapi_api_token

# Database Configuration (MySQL)
DB_HOST=localhost
DB_USER=biagiotti_user
DB_PASSWORD=your_secure_password
DB_NAME=biagiotti_blog
DB_PORT=3306
DB_SSL=false

# SMTP Configuration
SMTP_HOST=mail.biagiotti.me
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password
SMTP_FROM=<EMAIL>

# Site Configuration
PUBLIC_SITE_URL=https://marcobiagiotti.me
PUBLIC_SITE_NAME=Marco Biagiotti Blog

# Analytics (Optional)
GA_MEASUREMENT_ID=G-XXXXXXXXXX
PLAUSIBLE_DOMAIN=marcobiagiotti.me

# Social Media
TWITTER_HANDLE=@marcobi
GITHUB_REPO=mbiagiottime/biagiotti-blog

# Strapi API Token (Required for fetching articles)
STRAPI_API_TOKEN=your_strapi_api_token
```

### 5. **Test Configurazione**

#### A. **Test Build**

```bash
# Test build per verificare configurazione
npm run build

# Dovrebbe completarsi senza errori
```

#### B. **Test Server Sviluppo**

```bash
# Avvia il server di sviluppo
npm run dev

# Apri http://localhost:4321
# Verifica che tutto funzioni
```

## 🎯 Configurazione Avanzata

### Personalizzazione Tema

Modifica `src/styles/global.css`:

```css
:root {
  /* Colori principali */
  --theme-color: #00a99d;
  --theme-color-rgb: 0, 169, 157;
  
  /* Colori testo */
  --heading-color: #2d3748;
  --default-color: #4a5568;
  
  /* Colori superficie */
  --surface-color: #ffffff;
  --background-color: #f7fafc;
}
```

### Logo e Branding

1. **Logo principale**: Sostituisci `public/logo.png`
2. **Favicon**: Sostituisci `public/favicon.svg`
3. **Immagini hero**: Aggiungi in `public/`

### Configurazione Commenti

Per abilitare i commenti con Utterances:

1. **Crea un repository pubblico** su GitHub
2. **Installa l'app Utterances**: [utteranc.es](https://utteranc.es)
3. **Abilita Issues** nel repository
4. **Configura** in `src/components/Comments.astro`:

```astro
const { 
  repo = 'username/repository-name',
  theme = 'github-light',
  issueTerm = 'pathname',
  label = 'comment'
} = Astro.props;
```

### Configurazione Analytics

#### Google Analytics

1. **Crea una proprietà** in Google Analytics
2. **Ottieni il Measurement ID** (formato: G-XXXXXXXXXX)
3. **Aggiungi al file `.env`**:
   ```bash
   GA_MEASUREMENT_ID=G-XXXXXXXXXX
   ```

#### Plausible Analytics

1. **Registrati** su [plausible.io](https://plausible.io)
2. **Aggiungi il tuo dominio**
3. **Configura** nel file `.env`:
   ```bash
   PLAUSIBLE_DOMAIN=tuodominio.com
   ```

### Configurazione Newsletter

#### Mailchimp

1. **Ottieni API Key** da Mailchimp
2. **Configura** in `.env`:
   ```bash
   MAILCHIMP_API_KEY=your_api_key
   MAILCHIMP_LIST_ID=your_list_id
   ```

#### ConvertKit

1. **Ottieni API Key** da ConvertKit
2. **Configura** in `.env`:
   ```bash
   CONVERTKIT_API_KEY=your_api_key
   CONVERTKIT_FORM_ID=your_form_id
   ```

## 📝 Primo Articolo

### 1. Crea il File

```bash
# Crea un nuovo articolo
touch src/content/blog/il-mio-primo-articolo.md
```

### 2. Aggiungi il Frontmatter

```yaml
---
title: 'Il Mio Primo Articolo'
description: 'Questo è il mio primo articolo sul blog'
pubDate: 2024-01-15
heroImage: '/blog-placeholder-1.jpg'
category: 'Tutorial'
tags: ['primo-post', 'blog', 'astro']
author: 'Il Tuo Nome'
featured: true
---
```

### 3. Scrivi il Contenuto

```markdown
# Il Mio Primo Articolo

Benvenuto nel mio blog! Questo è il mio primo articolo.

## Cosa troverai qui

- Tutorial su sviluppo web
- Guide pratiche
- Riflessioni personali

## Conclusioni

Grazie per aver letto il mio primo articolo!
```

### 4. Verifica il Risultato

Vai su `http://localhost:4321/blog` per vedere il tuo articolo.

## 🔍 Verifica Installazione

### Checklist Funzionalità

#### **Funzionalità Base**
- [ ] **Homepage** carica correttamente
- [ ] **Blog** mostra gli articoli
- [ ] **Articoli singoli** si aprono
- [ ] **Categorie e tag** funzionano
- [ ] **Commenti** sono configurati (se abilitati)
- [ ] **Analytics** traccia le visite (se configurato)

#### **Funzionalità Avanzate**
- [ ] **Ricerca blog** con feedback visivo funziona
- [ ] **Caricamento progressivo** articoli operativo
- [ ] **Back to Top** button appare durante scroll
- [ ] **Privacy Policy** modal si apre correttamente
- [ ] **Newsletter** accetta iscrizioni (API hybrid)
- [ ] **Form contatti** invia messaggi (API hybrid)

### Test di Build

```bash
# Testa la build di produzione
npm run build

# Verifica che non ci siano errori
npm run preview
```

### Test Performance

1. **Lighthouse**: Apri DevTools > Lighthouse
2. **PageSpeed Insights**: [pagespeed.web.dev](https://pagespeed.web.dev)
3. **GTmetrix**: [gtmetrix.com](https://gtmetrix.com)

## 🐛 Risoluzione Problemi

### Errori Comuni

#### Porta già in uso
```bash
# Se la porta 4321 è occupata
npm run dev -- --port 3000
```

#### Dipendenze mancanti
```bash
# Reinstalla tutto
rm -rf node_modules package-lock.json
npm install
```

#### Cache corrotta
```bash
# Pulisci cache Astro
rm -rf node_modules/.astro
rm -rf dist
npm run dev
```

### Log di Debug

```bash
# Avvia con debug completo
DEBUG=astro:* npm run dev

# Build con log dettagliati
npm run build -- --verbose
```

## 📞 Supporto

Se incontri problemi durante l'installazione:

1. **Controlla i log** per errori specifici
2. **Verifica i prerequisiti** (versioni Node.js, npm)
3. **Consulta la documentazione** Astro: [docs.astro.build](https://docs.astro.build)
4. **Apri un issue** su GitHub se il problema persiste

---

### 6. **Test Funzionalità**

#### A. **Test Newsletter**

```bash
# Test API newsletter
curl -X POST http://localhost:4321/api/newsletter \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","name":"Test User"}'

# Verifica nel database
mysql -u biagiotti_user -p biagiotti_blog -e "SELECT * FROM newsletter_subscriptions;"
```

#### B. **Test Contatti**

```bash
# Test API contatti
curl -X POST http://localhost:4321/api/contact \
  -H "Content-Type: application/json" \
  -d '{"name":"Test User","email":"<EMAIL>","message":"Test message"}'

# Verifica nel database
mysql -u biagiotti_user -p biagiotti_blog -e "SELECT * FROM contact_submissions;"
```

## ✅ Checklist Installazione

- [ ] Node.js 18+ installato
- [ ] MySQL 5.7+ installato e configurato
- [ ] Repository clonato
- [ ] Dipendenze npm installate
- [ ] Database creato e schema importato
- [ ] File .env configurato (inclusi `STRAPI_BASE_URL` e `STRAPI_API_TOKEN`)
- [ ] Build test completato con successo
- [ ] Server sviluppo funzionante
- [ ] API newsletter testata
- [ ] API contatti testata
- [ ] Email SMTP configurato (opzionale)
- [ ] Analytics configurato (opzionale)

## 🎉 Installazione Completata!

Se tutti i test sono passati, la tua installazione è completa!

### **Prossimi Passi**:
1. **Personalizza** il contenuto in `src/content/blog/`
2. **Modifica** colori e stili in `src/styles/global.css`
3. **Aggiorna** informazioni in `src/consts.ts`
4. **Configura** il deploy per la produzione

### **Risorse Utili**:
- [Setup MySQL](MYSQL_SETUP.md)
- [Migrazione Database](MYSQL_MIGRATION_SUMMARY.md)
- [Troubleshooting](TROUBLESHOOTING.md)
- [Correzioni Effettuate](FIXES_SUMMARY.md)

---

**Buon blogging! 🚀**
