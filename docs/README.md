# 📚 Documentazione Marco Biagiotti Blog

Benvenuto nella documentazione completa del blog Marco <PERSON>! Questa cartella contiene tutte le guide necessarie per installare, configurare e mantenere il blog.

## 🚀 Guide di Avvio Rapido

### **Per Iniziare**
1. **[README Principale](../README.md)** - Panoramica completa del progetto
2. **[Installazione](INSTALLATION.md)** - Guida passo-passo per l'installazione
3. **[Setup MySQL](MYSQL_SETUP.md)** - Configurazione database completa

### **Per Sviluppatori**
1. **[Correzioni Effettuate](FIXES_SUMMARY.md)** - Tutte le 22 modifiche e miglioramenti
2. **[Migrazione MySQL](MYSQL_MIGRATION_SUMMARY.md)** - Dettagli migrazione da Supabase
3. **[Pulizia Progetto](CLEANUP_SUMMARY.md)** - File rimossi e organizzazione
4. **[Riepilogo Progetto](PROJECT_COMPLETION_SUMMARY.md)** - Stato finale e completamento

## 📖 Guide Dettagliate

### **🔧 Installazione e Setup**

#### **[INSTALLATION.md](INSTALLATION.md)**
Guida completa per l'installazione del blog con tutte le funzionalità.

**Contenuti**:
- ✅ Prerequisiti software (Node.js, MySQL, Git)
- ✅ Clone repository e installazione dipendenze
- ✅ Configurazione database MySQL
- ✅ Setup file environment (.env)
- ✅ Test configurazione e funzionalità
- ✅ Checklist installazione completa

**Tempo stimato**: 30-45 minuti

#### **[MYSQL_SETUP.md](MYSQL_SETUP.md)**
Guida specializzata per la configurazione MySQL.

**Contenuti**:
- ✅ Installazione e configurazione MySQL
- ✅ Creazione database e utenti
- ✅ Importazione schema completo
- ✅ Test connessione e funzionalità
- ✅ Sicurezza e best practices
- ✅ Backup e manutenzione

**Tempo stimato**: 20-30 minuti

## ⚙️ Configurazione

### Configurazione Base

Modifica il file `src/consts.ts`:

```typescript
export const SITE_TITLE = 'Marco Biagiotti Blog';
export const SITE_DESCRIPTION = 'Blog personale di Marco Biagiotti';
export const SITE_URL = 'https://tuodominio.com';
```

### Configurazione SEO

Aggiorna `src/components/BaseHead.astro`:

```astro
<!-- Meta tags personalizzati -->
<meta property="og:site_name" content="Marco Biagiotti Blog" />
<meta name="twitter:site" content="@tuoaccount" />
```

### Configurazione Analytics

#### Google Analytics
```typescript
// In src/components/Analytics.astro
const GA_MEASUREMENT_ID = 'G-XXXXXXXXXX';
```

#### Plausible Analytics
```typescript
const PLAUSIBLE_DOMAIN = 'tuodominio.com';
```

### Configurazione Commenti

Il sistema utilizza **Utterances** con GitHub:

```astro
<!-- In src/components/Comments.astro -->
repo = 'mbiagiottime/biagiotti-blog'
```

**Requisiti per Utterances:**
1. Repository pubblico su GitHub
2. App Utterances installata: [utteranc.es](https://utteranc.es)
3. Issues abilitati nel repository

## 📝 Gestione Contenuti

### Struttura Contenuti

```
src/content/blog/
├── primo-articolo.md
├── secondo-articolo.mdx
└── assets/
    ├── immagine1.jpg
    └── immagine2.png
```

### Frontmatter Articoli

```yaml
---
title: 'Titolo del tuo articolo'
description: 'Descrizione breve per SEO'
pubDate: 2024-01-15
updatedDate: 2024-01-16  # Opzionale
heroImage: '/blog-placeholder-1.jpg'
category: 'Tutorial'     # Categoria principale
tags: ['astro', 'web', 'development']
author: 'Marco Biagiotti'
featured: true           # Articolo in evidenza
readingTime: 5          # Minuti di lettura
---
```

### Aggiungere Nuovi Articoli

1. **Crea il file**: `src/content/blog/nuovo-articolo.md`
2. **Aggiungi frontmatter** con tutti i metadati
3. **Scrivi il contenuto** in Markdown o MDX
4. **Aggiungi immagini** in `public/` o `src/content/blog/assets/`

### Categorie e Tag

- **Categorie**: Classificazione principale (es. "Tutorial", "Guide", "News")
- **Tag**: Etichette specifiche (es. "astro", "javascript", "seo")

Le pagine vengono generate automaticamente:
- `/categories/tutorial/` - Tutti gli articoli della categoria
- `/tags/astro/` - Tutti gli articoli con il tag

## 🎨 Personalizzazione

### Colori e Tema

Modifica le variabili CSS in `src/styles/global.css`:

```css
:root {
  --theme-color: #00a99d;        /* Verde principale */
  --theme-color-rgb: 0, 169, 157;
  --heading-color: #2d3748;      /* Colore titoli */
  --default-color: #4a5568;      /* Colore testo */
  --surface-color: #ffffff;      /* Sfondo carte */
}
```

### Logo e Branding

1. **Logo**: Sostituisci `public/logo.png`
2. **Favicon**: Sostituisci `public/favicon.svg`
3. **Immagini**: Aggiungi in `public/`

### Layout e Componenti

- **Header**: `src/components/Header.astro`
- **Footer**: `src/components/Footer.astro`
- **Layout principale**: `src/layouts/Layout.astro`

## 🚀 Ottimizzazione

### Performance

#### Immagini
```astro
<!-- Usa il componente Image di Astro -->
import { Image } from 'astro:assets';
import heroImage from '../assets/hero.jpg';

<Image src={heroImage} alt="Descrizione" width={800} height={400} />
```

#### Lazy Loading
```astro
<!-- Caricamento lazy automatico -->
<img src="/image.jpg" loading="lazy" alt="Descrizione" />
```

### SEO

#### Meta Tags Dinamici
```astro
---
const { title, description, image } = Astro.props;
---
<meta property="og:title" content={title} />
<meta property="og:description" content={description} />
<meta property="og:image" content={image} />
```

#### Schema Markup
Il blog include automaticamente:
- **Article Schema** per gli articoli
- **BreadcrumbList** per la navigazione
- **Organization Schema** per il sito

### Velocità

#### Bundle Optimization
```javascript
// astro.config.mjs
export default defineConfig({
  vite: {
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
          },
        },
      },
    },
  },
});
```

#### Preload Critico
```astro
<!-- In BaseHead.astro -->
<link rel="preload" href="/fonts/atkinson-regular.woff" as="font" type="font/woff" crossorigin>
```

## 🌐 Deploy

### Vercel (Raccomandato)

```bash
# Installa Vercel CLI
npm i -g vercel

# Deploy
vercel

# Deploy di produzione
vercel --prod
```

**Configurazione Vercel** (`vercel.json`):
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "framework": "astro"
}
```

### Netlify

```bash
# Build command
npm run build

# Publish directory
dist
```

### GitHub Pages

```yaml
# .github/workflows/deploy.yml
name: Deploy to GitHub Pages
on:
  push:
    branches: [ main ]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - run: npm ci
      - run: npm run build
      - uses: actions/deploy-pages@v1
        with:
          artifact_name: github-pages
          path: dist
```

### Variabili d'Ambiente

Crea `.env`:
```bash
# Analytics
GA_MEASUREMENT_ID=G-XXXXXXXXXX
PLAUSIBLE_DOMAIN=tuodominio.com

# Newsletter
MAILCHIMP_API_KEY=your_api_key
CONVERTKIT_API_KEY=your_api_key

# Database (opzionale)
DATABASE_URL=your_database_url
```

## 🔧 Manutenzione

### Aggiornamenti

```bash
# Controlla aggiornamenti
npm outdated

# Aggiorna dipendenze
npm update

# Aggiorna Astro
npm install astro@latest
```

### Backup

#### Contenuti
```bash
# Backup contenuti
tar -czf backup-content-$(date +%Y%m%d).tar.gz src/content/
```

#### Database (se utilizzato)
```bash
# Backup database
pg_dump $DATABASE_URL > backup-$(date +%Y%m%d).sql
```

### Monitoraggio

#### Analytics
- **Google Analytics**: Traffico e comportamento utenti
- **Google Search Console**: Performance SEO
- **Plausible**: Analytics privacy-friendly

#### Performance
- **Lighthouse**: Audit automatici
- **WebPageTest**: Test velocità
- **GTmetrix**: Monitoraggio performance

## 🐛 Troubleshooting

### Errori Comuni

#### 1. Font Bootstrap Icons non caricati
**Problema**: `404 Not Found` per i font
**Soluzione**: Verifica che i percorsi in `public/bootstrap-icons.css` siano assoluti:
```css
src: url("/fonts/bootstrap-icons.woff2") format("woff2");
```

#### 2. Errore JavaScript "Unexpected identifier 'as'"
**Problema**: Sintassi TypeScript in script client-side
**Soluzione**: Rimuovi type annotations:
```javascript
// ❌ Errato
function showMessage(message: string, type: 'success' | 'error') {}

// ✅ Corretto
function showMessage(message, type) {}
```

#### 3. Utterances non funziona
**Problema**: Repository non configurato
**Soluzione**:
1. Verifica che il repository sia pubblico
2. Installa l'app Utterances
3. Abilita Issues nel repository

#### 4. Build fallisce
**Problema**: Errori di compilazione
**Soluzione**:
```bash
# Pulisci cache
rm -rf node_modules/.astro
rm -rf dist

# Reinstalla dipendenze
npm ci

# Rebuild
npm run build
```

### Debug

#### Modalità Debug
```bash
# Avvia con debug
DEBUG=astro:* npm run dev
```

#### Log Dettagliati
```bash
# Build con log verbosi
npm run build -- --verbose
```

### Supporto

- **Documentazione Astro**: [docs.astro.build](https://docs.astro.build)
- **Community Discord**: [astro.build/chat](https://astro.build/chat)
- **GitHub Issues**: [github.com/withastro/astro](https://github.com/withastro/astro)

---

### **Contatti**
- 📧 **Email**: <EMAIL>
- 💼 **LinkedIn**: [linkedin.com/in/marcobiagiotti](https://www.linkedin.com/in/marcobiagiotti/)
- 🐛 **Issues**: [GitHub Issues](https://github.com/mbiagiottime/biagiotti-blog/issues)

## ✅ Checklist Documentazione

- [x] **README principale** aggiornato con tutte le funzionalità
- [x] **Guida installazione** completa con MySQL
- [x] **Setup database** dettagliato e testato
- [x] **Migrazione documentata** con tutti i dettagli
- [x] **Correzioni catalogate** con esempi di codice
- [x] **Pulizia documentata** con motivazioni
- [x] **Troubleshooting** per problemi comuni
- [x] **Indice documentazione** (questo file)

## 🎉 Documentazione Completa!

La documentazione del blog Marco Biagiotti è ora **completa e aggiornata**!

### **Caratteristiche**:
- ✅ **Guide passo-passo** per ogni aspetto
- ✅ **Esempi di codice** funzionanti
- ✅ **Troubleshooting** dettagliato
- ✅ **Best practices** per manutenzione
- ✅ **Struttura organizzata** e navigabile

### **Prossimi Passi**:
1. **Inizia** con [Installazione](INSTALLATION.md)
2. **Configura** il [Database MySQL](MYSQL_SETUP.md)
3. **Consulta** [Troubleshooting](TROUBLESHOOTING.md) se necessario

**Buon lavoro con il tuo blog professionale! 🚀**

---

*Documentazione aggiornata il: Dicembre 2024*
*Versione: 2.0 - MySQL Edition*
