#!/usr/bin/env node

// Carica le variabili d'ambiente dal file .env
import { config as dotenvConfig } from 'dotenv';
dotenvConfig();

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { getConfig } from './strapi-config.js';
import { 
  ImageManager, 
  SEOManager, 
  RichTextConverter, 
  FileManager, 
  Validator 
} from './strapi-utils.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Carica configurazione
const config = getConfig();
const STRAPI_API_TOKEN = process.env.STRAPI_API_TOKEN;
const BLOG_CONTENT_DIR = path.join(__dirname, '..', config.output.blogDir);

class StrapiSync {
  constructor() {
    this.config = config;
    this.baseUrl = config.baseUrl;
    this.token = STRAPI_API_TOKEN;
    this.imageManager = new ImageManager(this.baseUrl, config.richTextConfig.imageConfig.localImageDir);
    
    if (!this.token) {
      console.warn('⚠️  STRAPI_API_TOKEN non impostato. Alcune operazioni potrebbero non funzionare.');
    }
    
    // Valida configurazione
    const configErrors = Validator.validateConfig(config);
    if (configErrors.length > 0) {
      console.error('❌ Errori di configurazione:', configErrors);
      process.exit(1);
    }
  }

  /**
   * Effettua una richiesta all'API Strapi
   */
  async fetchFromStrapi(endpoint, options = {}) {
    const url = `${this.baseUrl}/api${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      ...(this.token && { 'Authorization': `Bearer ${this.token}` }),
      ...options.headers
    };

    try {
      const response = await fetch(url, { ...options, headers });
      
      if (!response.ok) {
        throw new Error(`Errore HTTP: ${response.status} - ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`Errore nella richiesta a ${url}:`, error.message);
      throw error;
    }
  }

  /**
   * Recupera tutti gli articoli da Strapi
   */
  async getArticles() {
    try {
      console.log('📡 Recupero articoli da Strapi...');
      
      const endpoint = `${config.articlesEndpoint}?${config.populateParams}`;
      const response = await this.fetchFromStrapi(endpoint);
      
      if (!response.data) {
        throw new Error('Nessun dato ricevuto da Strapi');
      }
      
      // Applica filtri se configurati
      let articles = response.data;
      
      if (config.filters.publishedOnly) {
        articles = articles.filter(article => 
          article.attributes.publishedAt || !article.attributes.hasOwnProperty('publishedAt')
        );
      }
      
      console.log(`✅ Trovati ${articles.length} articoli`);
      return articles;
    } catch (error) {
      console.error('❌ Errore nel recupero degli articoli:', error.message);
      return [];
    }
  }

  /**
   * Genera il frontmatter YAML per Astro
   */
  async generateFrontmatter(article) {
    // Gestione dell'immagine
    let heroImage = '';
    if (article.Immagine) {
      heroImage = await this.imageManager.processImage(
        article.Immagine,
        config.richTextConfig.imageConfig.downloadImages
      );
    }

    // Gestione della data
    const pubDate = article.Data
      ? new Date(article.Data).toISOString().split('T')[0]
      : new Date().toISOString().split('T')[0];

    // Estrazione keywords per tags
    const tags = article.seo?.keywords
      ? article.seo.keywords.split(',').map(tag => tag.trim()).filter(Boolean)
      : [];

    // Metadati SEO
    const seoMetadata = SEOManager.generateSEOMetadata(
      article.seo,
      article.openGraph,
      article
    );

    // Schema.org
    const articleSchema = SEOManager.generateArticleSchema(article, this.baseUrl);

    const frontmatter = {
      title: article.Titolo || 'Titolo non disponibile',
      description: article.Sommario || article.seo?.metaDescription || '',
      pubDate: pubDate,
      ...(heroImage && { heroImage }),
      ...(tags.length > 0 && { tags }),
      author: config.output.defaultAuthor,
      category: config.output.defaultCategory,
      featured: false,
      // Publication control - importa come draft per default
      draft: true,
      published: false,
      publishDate: new Date(pubDate),
      ...seoMetadata
    };

    return frontmatter;
  }

  /**
   * Converte un articolo Strapi in file Markdown
   */
  async convertArticleToMarkdown(article) {
    // Valida l'articolo
    const validationErrors = Validator.validateArticle(article);
    if (validationErrors.length > 0) {
      throw new Error(`Articolo non valido: ${validationErrors.join(', ')}`);
    }

    const frontmatter = await this.generateFrontmatter(article);

    // Genera il contenuto Markdown
    const content = RichTextConverter.convertToMarkdown(article.Contenuto);

    // Crea il file Markdown completo
    const yamlFrontmatter = this.generateYAMLFrontmatter(frontmatter);

    const markdown = `---
${yamlFrontmatter}
---

${content}`;

    return {
      filename: FileManager.generateSafeFilename(article.Titolo, article.id),
      content: markdown,
      frontmatter
    };
  }

  /**
   * Genera il frontmatter YAML
   */
  generateYAMLFrontmatter(frontmatter) {
    return Object.entries(frontmatter)
      .map(([key, value]) => {
        if (value === null || value === undefined) return null;

        if (Array.isArray(value)) {
          return `${key}: [${value.map(v => `'${v}'`).join(', ')}]`;
        }

        if (typeof value === 'object' && value instanceof Date) {
          return `${key}: ${value.toISOString()}`;
        }

        if (typeof value === 'object') {
          // Per oggetti complessi, convertiamo in stringa JSON come commento
          console.warn(`⚠️  Oggetto complesso ignorato nel frontmatter: ${key}`);
          return null;
        }

        if (typeof value === 'string' && value.includes('\n')) {
          return `${key}: |\n  ${value.split('\n').join('\n  ')}`;
        }

        if (typeof value === 'boolean') {
          return `${key}: ${value}`;
        }

        if (typeof value === 'number') {
          return `${key}: ${value}`;
        }

        // Escape delle virgolette nelle stringhe
        const escapedValue = String(value).replace(/'/g, "''");
        return `${key}: '${escapedValue}'`;
      })
      .filter(Boolean)
      .join('\n');
  }

  /**
   * Salva un file Markdown
   */
  async saveMarkdownFile(filename, content) {
    try {
      // Assicurati che la directory esista
      await fs.mkdir(BLOG_CONTENT_DIR, { recursive: true });
      
      const filePath = path.join(BLOG_CONTENT_DIR, filename);
      
      // Backup del file esistente se configurato
      if (config.backupExisting && await FileManager.fileExists(filePath)) {
        const backupPath = await FileManager.backupFile(filePath, config.backupDir);
        if (backupPath && config.verbose) {
          console.log(`📦 Backup creato: ${backupPath}`);
        }
      }
      
      // Salva il file (o simula se dryRun)
      if (config.dryRun) {
        console.log(`🔍 [DRY RUN] Salverei: ${filename}`);
        return filePath;
      }
      
      await fs.writeFile(filePath, content, 'utf8');
      
      if (config.verbose) {
        console.log(`✅ Salvato: ${filename}`);
      }
      
      return filePath;
    } catch (error) {
      console.error(`❌ Errore nel salvare ${filename}:`, error.message);
      throw error;
    }
  }

  /**
   * Sincronizza tutti gli articoli
   */
  async syncAll() {
    try {
      console.log('🚀 Inizio sincronizzazione...\n');
      
      const articles = await this.getArticles();
      
      if (articles.length === 0) {
        console.log('ℹ️  Nessun articolo da sincronizzare');
        return;
      }
      
      const results = [];
      
      for (const article of articles) {
        try {
          if (config.verbose) {
            console.log(`📝 Elaborazione: ${article.Titolo}`);
          }

          const { filename, content, frontmatter } = await this.convertArticleToMarkdown(article);
          await this.saveMarkdownFile(filename, content);

          results.push({
            success: true,
            filename,
            title: article.Titolo
          });

        } catch (error) {
          console.error(`❌ Errore nell'elaborazione dell'articolo ${article.id}:`, error.message);
          results.push({
            success: false,
            error: error.message,
            title: article.Titolo || `ID: ${article.id}`
          });
        }
      }
      
      // Riepilogo
      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;
      
      console.log(`\n📊 Riepilogo sincronizzazione:`);
      console.log(`✅ Successi: ${successful}`);
      console.log(`❌ Errori: ${failed}`);
      
      if (failed > 0) {
        console.log('\n❌ Articoli con errori:');
        results.filter(r => !r.success).forEach(r => {
          console.log(`  - ${r.title}: ${r.error}`);
        });
      }
      
    } catch (error) {
      console.error('💥 Errore generale nella sincronizzazione:', error.message);
    }
  }

  /**
   * Lista gli articoli disponibili
   */
  async listArticles() {
    try {
      const articles = await this.getArticles();
      
      if (articles.length === 0) {
        console.log('ℹ️  Nessun articolo trovato');
        return;
      }
      
      console.log('📋 Articoli disponibili:\n');
      articles.forEach((article, index) => {
        console.log(`${index + 1}. ${article.Titolo}`);
        console.log(`   ID: ${article.id}`);
        console.log(`   Data: ${article.Data ? new Date(article.Data).toLocaleDateString('it-IT') : 'Non specificata'}`);
        console.log(`   Sommario: ${article.Sommario ? article.Sommario.substring(0, 100) + '...' : 'Non disponibile'}`);
        console.log('');
      });
      
    } catch (error) {
      console.error('❌ Errore nel recupero della lista:', error.message);
    }
  }

  /**
   * Sincronizza un singolo articolo per ID o DocumentID
   */
  async syncById(articleId) {
    try {
      console.log(`📡 Recupero articolo ID: ${articleId}...`);

      // Prima prova con l'ID diretto
      let endpoint = `${config.articlesEndpoint}/${articleId}?${config.populateParams}`;
      let response;

      try {
        response = await this.fetchFromStrapi(endpoint);
      } catch (error) {
        // Se fallisce, prova a cercare l'articolo nella lista per ID numerico
        console.log(`⚠️  Tentativo diretto fallito, cerco nella lista...`);
        const allResponse = await this.fetchFromStrapi(`${config.articlesEndpoint}?${config.populateParams}`);
        const allArticles = allResponse.data || [];
        const article = allArticles.find(a => a.id == articleId || a.documentId === articleId);

        if (!article) {
          throw new Error(`Articolo con ID ${articleId} non trovato`);
        }

        // Usa il documentId per recuperare l'articolo completo
        endpoint = `${config.articlesEndpoint}/${article.documentId}?${config.populateParams}`;
        response = await this.fetchFromStrapi(endpoint);
      }

      if (!response.data) {
        throw new Error('Articolo non trovato');
      }

      const article = response.data;
      console.log(`📝 Elaborazione: ${article.Titolo}`);

      const { filename, content } = await this.convertArticleToMarkdown(article);
      await this.saveMarkdownFile(filename, content);

      console.log(`✅ Articolo sincronizzato: ${filename}`);

    } catch (error) {
      console.error(`❌ Errore nella sincronizzazione dell'articolo ${articleId}:`, error.message);
    }
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'help';
  
  const sync = new StrapiSync();
  
  switch (command) {
    case 'sync':
      if (args[1]) {
        await sync.syncById(args[1]);
      } else {
        await sync.syncAll();
      }
      break;
      
    case 'list':
      await sync.listArticles();
      break;
      
    case 'help':
    default:
      console.log(`
🔄 Strapi Blog Sync Tool

Comandi disponibili:
  sync [id]   Sincronizza tutti gli articoli o un articolo specifico
  list        Mostra la lista degli articoli disponibili
  help        Mostra questo messaggio

Esempi:
  node scripts/strapi-sync.js sync        # Sincronizza tutti
  node scripts/strapi-sync.js sync 123   # Sincronizza articolo ID 123
  node scripts/strapi-sync.js list       # Lista articoli

Configurazione:
  Imposta la variabile d'ambiente STRAPI_API_TOKEN per l'autenticazione.
  
  export STRAPI_API_TOKEN="your-token-here"
  node scripts/strapi-sync.js sync

Opzioni ambiente:
  NODE_ENV=production     # Usa configurazione produzione
  NODE_ENV=development    # Usa configurazione sviluppo (default)
      `);
      break;
  }
}

// Esegui solo se chiamato direttamente
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export default StrapiSync;
