# ⏰ Guida alla Pubblicazione Programmata

Questo sistema ti permette di programmare la pubblicazione degli articoli per date e orari futuri, con pubblicazione automatica.

## 🎯 Funzionalità

### ⏰ Programmazione Manuale
- Imposta data e ora specifiche per la pubblicazione
- Supporta formati flessibili: `YYYY-MM-DD` o `YYYY-MM-DD HH:MM`
- Gestione automatica del fuso orario del server

### 🤖 Pubblicazione Automatica
- Gli articoli diventano visibili automaticamente alla data programmata
- Script di controllo per pubblicazione manuale o automatica
- Modalità daemon per controllo continuo

### 📊 Monitoraggio
- Visualizzazione articoli programmati
- Statistiche e prossime pubblicazioni
- Notifiche opzionali via webhook

## 🚀 Come Usare

### 1. Programmazione Interattiva

```bash
npm run manage-blog
```

Nell'interfaccia interattiva:
```
> 1 schedule 2024-02-15 10:30
⏰ Articolo "Titolo" programmato per 15/02/2024 alle 10:30
```

### 2. Programmazione da Riga di Comando

```bash
# Programma per una data specifica (ore 09:00)
npm run blog-schedule 1 2024-02-15

# Programma con orario specifico
npm run blog-schedule 1 "2024-02-15 14:30"

# Programma per domani alle 10:00
npm run blog-schedule 2 "2024-01-16 10:00"
```

### 3. Controllo Pubblicazione

```bash
# Pubblica manualmente articoli pronti
npm run blog-auto-publish

# Controlla statistiche
npm run auto-publisher-stats

# Modalità daemon (controllo continuo)
npm run auto-publisher-daemon
```

## 📋 Stati degli Articoli

### ⏰ SCHEDULED (Programmato)
- **Stato**: `draft: false`, `published: false`, `autoPublish: true`
- **Condizione**: `scheduledDate` nel futuro
- **Visibilità**: ❌ Non visibile (ancora)
- **Comportamento**: Diventerà automaticamente LIVE alla data programmata

### 🚀 READY (Pronto)
- **Stato**: `draft: false`, `published: false`, `autoPublish: true`
- **Condizione**: `scheduledDate` nel passato
- **Visibilità**: ✅ Visibile (se il sistema è aggiornato)
- **Comportamento**: Pronto per pubblicazione automatica

## 🔧 Configurazione Automazione

### Cron Job (Linux/Mac)

Aggiungi al crontab (`crontab -e`):

```bash
# Controlla ogni 15 minuti
*/15 * * * * cd /path/to/your/blog && npm run auto-publisher >> /var/log/blog-autopublisher.log 2>&1

# Controlla ogni ora
0 * * * * cd /path/to/your/blog && npm run auto-publisher

# Controlla ogni giorno alle 9:00
0 9 * * * cd /path/to/your/blog && npm run auto-publisher
```

### Task Scheduler (Windows)

1. Apri Task Scheduler
2. Crea Attività di Base
3. Trigger: Ripeti ogni 15-60 minuti
4. Azione: Avvia programma
   - Programma: `node`
   - Argomenti: `scripts/auto-publisher.js run`
   - Inizia in: `C:\path\to\your\blog`

### GitHub Actions (CI/CD)

```yaml
# .github/workflows/auto-publish.yml
name: Auto Publish Scheduled Posts
on:
  schedule:
    - cron: '*/15 * * * *'  # Ogni 15 minuti
  workflow_dispatch:

jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run auto-publisher
        env:
          BLOG_WEBHOOK_URL: ${{ secrets.BLOG_WEBHOOK_URL }}
      - uses: stefanzweifel/git-auto-commit-action@v4
        with:
          commit_message: 'Auto-publish scheduled posts'
```

### Docker Container

```dockerfile
# Dockerfile.autopublisher
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
CMD ["npm", "run", "auto-publisher-daemon", "15"]
```

```bash
# Build e run
docker build -f Dockerfile.autopublisher -t blog-autopublisher .
docker run -d --name blog-autopublisher blog-autopublisher
```

## 📱 Notifiche

### Webhook Configuration

Imposta la variabile d'ambiente per ricevere notifiche:

```bash
export BLOG_WEBHOOK_URL="https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
```

### Slack Integration

```json
{
  "text": "🎉 Blog Auto-Publisher: Pubblicati 2 articoli!",
  "published": 2,
  "total": 2,
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Discord Webhook

```bash
export BLOG_WEBHOOK_URL="https://discord.com/api/webhooks/YOUR/DISCORD/WEBHOOK"
```

## 🎮 Esempi Pratici

### Scenario 1: Pubblicazione Settimanale

```bash
# Lunedì alle 9:00
npm run blog-schedule 1 "2024-01-22 09:00"

# Mercoledì alle 14:00  
npm run blog-schedule 2 "2024-01-24 14:00"

# Venerdì alle 10:00
npm run blog-schedule 3 "2024-01-26 10:00"
```

### Scenario 2: Campagna di Contenuti

```bash
# Serie di 5 articoli, uno al giorno alle 10:00
npm run blog-schedule 1 "2024-02-01 10:00"
npm run blog-schedule 2 "2024-02-02 10:00"
npm run blog-schedule 3 "2024-02-03 10:00"
npm run blog-schedule 4 "2024-02-04 10:00"
npm run blog-schedule 5 "2024-02-05 10:00"
```

### Scenario 3: Controllo Manuale

```bash
# Controlla cosa è programmato
npm run auto-publisher-stats

# Pubblica manualmente se necessario
npm run blog-auto-publish

# Modalità dry-run per vedere cosa verrebbe pubblicato
node scripts/auto-publisher.js dry-run
```

## 🔍 Monitoraggio e Debug

### Visualizza Articoli Programmati

```bash
npm run blog-list
```

Output:
```
ID  | Stato        | Titolo                     | Data       | Programmazione
----|--------------|----------------------------|------------|---------------
 1  | ⏰ SCHEDULED | Tutorial React Hooks       | 15/01/2024 | 20/01/2024 10:00
 2  | 🚀 READY     | Guida TypeScript           | 14/01/2024 | Pronto per pubblicazione
 3  | ✅ LIVE      | Intro JavaScript           | 13/01/2024 |
```

### Log e Debugging

```bash
# Modalità verbose
DEBUG=true npm run auto-publisher

# Log in file
npm run auto-publisher >> autopublisher.log 2>&1

# Modalità dry-run
node scripts/auto-publisher.js dry-run
```

## ⚠️ Considerazioni Importanti

### Fuso Orario
- Le date sono interpretate nel fuso orario del server
- Usa sempre il formato ISO per precisione: `2024-01-15T10:30:00.000Z`
- Considera il fuso orario del tuo pubblico

### Backup e Sicurezza
- Il sistema crea backup automatici prima delle modifiche
- Gli articoli programmati sono reversibili
- Testa sempre in ambiente di sviluppo

### Performance
- Il controllo automatico è leggero e veloce
- Frequenza consigliata: 15-60 minuti
- Evita controlli troppo frequenti (< 5 minuti)

### Monitoraggio
- Controlla regolarmente i log
- Imposta notifiche per errori
- Verifica che l'automazione funzioni

## 🆘 Risoluzione Problemi

### Articolo Non Pubblicato Automaticamente

1. **Controlla lo stato**:
   ```bash
   npm run blog-list
   ```

2. **Verifica la data**:
   ```bash
   npm run auto-publisher-stats
   ```

3. **Pubblica manualmente**:
   ```bash
   npm run blog-auto-publish
   ```

### Errori di Formato Data

```bash
# ❌ Formato sbagliato
npm run blog-schedule 1 15/01/2024

# ✅ Formato corretto
npm run blog-schedule 1 2024-01-15
npm run blog-schedule 1 "2024-01-15 10:30"
```

### Automazione Non Funziona

1. **Verifica cron job**:
   ```bash
   crontab -l
   ```

2. **Testa manualmente**:
   ```bash
   npm run auto-publisher
   ```

3. **Controlla i log**:
   ```bash
   tail -f /var/log/blog-autopublisher.log
   ```

## 🎯 Best Practices

1. **Pianificazione**: Programma con almeno 1 ora di anticipo
2. **Orari**: Scegli orari quando il tuo pubblico è più attivo
3. **Frequenza**: Non sovraccaricare con troppe pubblicazioni
4. **Controllo**: Verifica regolarmente gli articoli programmati
5. **Backup**: Mantieni sempre backup dei contenuti
6. **Test**: Testa la programmazione in ambiente di sviluppo

La pubblicazione programmata ti dà il controllo completo sui tempi di pubblicazione, permettendoti di mantenere un flusso costante di contenuti anche quando non sei disponibile! ⏰✨
