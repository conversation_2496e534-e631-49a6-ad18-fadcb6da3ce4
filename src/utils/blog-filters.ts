import type { CollectionEntry } from 'astro:content';

type BlogPost = CollectionEntry<'blog'>;

/**
 * Filtra gli articoli pubblicati (non draft e published = true)
 * Include anche la logica per la pubblicazione programmata
 */
export function getPublishedPosts(posts: BlogPost[]): BlogPost[] {
  const now = new Date();

  return posts.filter(post => {
    const {
      draft = false,
      published = true,
      scheduledDate,
      autoPublish = false
    } = post.data;

    // Se è una bozza, non mostrare
    if (draft) return false;

    // Se non è pubblicato e non ha pubblicazione programmata
    if (!published && !autoPublish) return false;

    // Se ha una data programmata, controlla se è arrivato il momento
    if (scheduledDate && autoPublish) {
      const scheduleTime = new Date(scheduledDate);
      return scheduleTime <= now;
    }

    // Altrimenti usa il flag published standard
    return published;
  });
}

/**
 * Filtra gli articoli in bozza
 */
export function getDraftPosts(posts: BlogPost[]): BlogPost[] {
  return posts.filter(post => {
    const { draft = false } = post.data;
    return draft;
  });
}

/**
 * Filtra gli articoli in pausa (non draft ma published = false e senza programmazione)
 */
export function getPausedPosts(posts: BlogPost[]): BlogPost[] {
  return posts.filter(post => {
    const {
      draft = false,
      published = true,
      autoPublish = false
    } = post.data;
    return !draft && !published && !autoPublish;
  });
}

/**
 * Filtra gli articoli programmati per il futuro
 */
export function getScheduledPosts(posts: BlogPost[]): BlogPost[] {
  const now = new Date();

  return posts.filter(post => {
    const {
      draft = false,
      scheduledDate,
      autoPublish = false
    } = post.data;

    if (draft || !autoPublish || !scheduledDate) return false;

    const scheduleTime = new Date(scheduledDate);
    return scheduleTime > now;
  });
}

/**
 * Filtra gli articoli che dovrebbero essere pubblicati automaticamente ora
 */
export function getPostsReadyToPublish(posts: BlogPost[]): BlogPost[] {
  const now = new Date();

  return posts.filter(post => {
    const {
      draft = false,
      published = false,
      scheduledDate,
      autoPublish = false
    } = post.data;

    if (draft || published || !autoPublish || !scheduledDate) return false;

    const scheduleTime = new Date(scheduledDate);
    return scheduleTime <= now;
  });
}

/**
 * Filtra gli articoli in evidenza tra quelli pubblicati
 */
export function getFeaturedPosts(posts: BlogPost[]): BlogPost[] {
  return getPublishedPosts(posts).filter(post => post.data.featured);
}

/**
 * Ordina gli articoli per data di pubblicazione (più recenti prima)
 */
export function sortPostsByDate(posts: BlogPost[]): BlogPost[] {
  return posts.sort((a, b) => {
    const dateA = new Date(a.data.pubDate);
    const dateB = new Date(b.data.pubDate);
    return dateB.getTime() - dateA.getTime();
  });
}

/**
 * Filtra gli articoli per categoria
 */
export function getPostsByCategory(posts: BlogPost[], category: string): BlogPost[] {
  return posts.filter(post => 
    post.data.category?.toLowerCase() === category.toLowerCase()
  );
}

/**
 * Filtra gli articoli per tag
 */
export function getPostsByTag(posts: BlogPost[], tag: string): BlogPost[] {
  return posts.filter(post => 
    post.data.tags?.some(postTag => 
      postTag.toLowerCase() === tag.toLowerCase()
    )
  );
}

/**
 * Ottieni tutti i tag unici dagli articoli pubblicati
 */
export function getAllTags(posts: BlogPost[]): string[] {
  const publishedPosts = getPublishedPosts(posts);
  const allTags = publishedPosts.flatMap(post => post.data.tags || []);
  return [...new Set(allTags)].sort();
}

/**
 * Ottieni tutte le categorie uniche dagli articoli pubblicati
 */
export function getAllCategories(posts: BlogPost[]): string[] {
  const publishedPosts = getPublishedPosts(posts);
  const allCategories = publishedPosts
    .map(post => post.data.category)
    .filter(Boolean) as string[];
  return [...new Set(allCategories)].sort();
}

/**
 * Cerca negli articoli pubblicati per titolo e contenuto
 */
export function searchPosts(posts: BlogPost[], query: string): BlogPost[] {
  const publishedPosts = getPublishedPosts(posts);
  const searchTerm = query.toLowerCase();
  
  return publishedPosts.filter(post => {
    const title = post.data.title.toLowerCase();
    const description = post.data.description.toLowerCase();
    const tags = (post.data.tags || []).join(' ').toLowerCase();
    
    return title.includes(searchTerm) || 
           description.includes(searchTerm) || 
           tags.includes(searchTerm);
  });
}

/**
 * Ottieni articoli correlati basati su tag comuni
 */
export function getRelatedPosts(posts: BlogPost[], currentPost: BlogPost, limit: number = 3): BlogPost[] {
  const publishedPosts = getPublishedPosts(posts);
  const currentTags = currentPost.data.tags || [];
  
  if (currentTags.length === 0) {
    // Se non ci sono tag, restituisci gli articoli più recenti
    return sortPostsByDate(publishedPosts)
      .filter(post => post.slug !== currentPost.slug)
      .slice(0, limit);
  }
  
  // Calcola il punteggio di correlazione basato sui tag comuni
  const relatedPosts = publishedPosts
    .filter(post => post.slug !== currentPost.slug)
    .map(post => {
      const postTags = post.data.tags || [];
      const commonTags = postTags.filter(tag => currentTags.includes(tag));
      return {
        post,
        score: commonTags.length
      };
    })
    .filter(item => item.score > 0)
    .sort((a, b) => b.score - a.score)
    .slice(0, limit)
    .map(item => item.post);
  
  // Se non ci sono abbastanza articoli correlati, aggiungi i più recenti
  if (relatedPosts.length < limit) {
    const recentPosts = sortPostsByDate(publishedPosts)
      .filter(post => 
        post.slug !== currentPost.slug && 
        !relatedPosts.some(related => related.slug === post.slug)
      )
      .slice(0, limit - relatedPosts.length);
    
    relatedPosts.push(...recentPosts);
  }
  
  return relatedPosts;
}

/**
 * Paginazione degli articoli
 */
export function paginatePosts(posts: BlogPost[], page: number = 1, postsPerPage: number = 10) {
  const startIndex = (page - 1) * postsPerPage;
  const endIndex = startIndex + postsPerPage;
  
  return {
    posts: posts.slice(startIndex, endIndex),
    currentPage: page,
    totalPages: Math.ceil(posts.length / postsPerPage),
    totalPosts: posts.length,
    hasNextPage: endIndex < posts.length,
    hasPrevPage: page > 1
  };
}

/**
 * Statistiche del blog
 */
export function getBlogStats(posts: BlogPost[]) {
  const publishedPosts = getPublishedPosts(posts);
  const draftPosts = getDraftPosts(posts);
  const pausedPosts = getPausedPosts(posts);
  const scheduledPosts = getScheduledPosts(posts);
  const readyToPublish = getPostsReadyToPublish(posts);

  return {
    total: posts.length,
    published: publishedPosts.length,
    drafts: draftPosts.length,
    paused: pausedPosts.length,
    scheduled: scheduledPosts.length,
    readyToPublish: readyToPublish.length,
    featured: getFeaturedPosts(posts).length,
    categories: getAllCategories(posts).length,
    tags: getAllTags(posts).length
  };
}
