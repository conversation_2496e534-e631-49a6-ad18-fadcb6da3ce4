---
interface Props {
  title?: string;
  description?: string;
  placeholder?: string;
  buttonText?: string;
}

const {
  title = "📧 Resta aggiornato",
  description = "Ricevi i nuovi articoli direttamente nella tua email. Niente spam, solo contenuti di qualità.",
  placeholder = "La tua email...",
  buttonText = "Iscriviti"
} = Astro.props;
---

<div class="newsletter-signup">
  <div class="newsletter-content">
    <h3>{title}</h3>
    <p>{description}</p>
    
    <form class="newsletter-form" id="newsletter-form">
      <div class="form-group">
        <input 
          type="email" 
          id="email" 
          name="email" 
          placeholder={placeholder}
          required 
          class="email-input"
        />
        <button type="submit" class="submit-btn" id="submit-btn">
          {buttonText}
        </button>
      </div>
      <div class="form-message" id="form-message"></div>
    </form>
    
    <p class="privacy-note">
      🔒 I tuoi dati sono al sicuro. Puoi annullare l'iscrizione in qualsiasi momento.
    </p>
  </div>
</div>

<script>
  // Newsletter signup functionality
  const form = document.getElementById('newsletter-form') as HTMLFormElement;
  const submitBtn = document.getElementById('submit-btn') as HTMLButtonElement;
  const messageDiv = document.getElementById('form-message') as HTMLDivElement;
  const emailInput = document.getElementById('email') as HTMLInputElement;

  form?.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const email = emailInput.value.trim();
    
    if (!email) {
      showMessage('Per favore inserisci un indirizzo email valido.', 'error');
      return;
    }

    // Disable button and show loading state
    submitBtn.disabled = true;
    submitBtn.textContent = 'Iscrizione...';

    try {
      await submitNewsletter(email);
    } catch (error) {
      console.error('Newsletter signup error:', error);
      showMessage('❌ Errore durante l\'iscrizione. Riprova più tardi.', 'error');
    } finally {
      // Re-enable button
      submitBtn.disabled = false;
      submitBtn.textContent = 'Iscriviti';
    }
  });

  function showMessage(message, type) {
    messageDiv.textContent = message;
    messageDiv.className = `form-message ${type}`;
    messageDiv.style.display = 'block';

    // Hide message after 5 seconds
    setTimeout(() => {
      messageDiv.style.display = 'none';
    }, 5000);
  }

  async function submitNewsletter(email, name) {
    try {
      const response = await fetch('/api/newsletter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, name }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        showMessage('Iscrizione completata! Controlla la tua email per la conferma.', 'success');
        form.reset();
      } else {
        showMessage(result.message || 'Si è verificato un errore. Riprova più tardi.', 'error');
      }
    } catch (error) {
      console.error('Newsletter signup error:', error);
      showMessage('Si è verificato un errore di connessione. Riprova più tardi.', 'error');
    }
  }
</script>

<style>
  .newsletter-signup {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin: 2rem 0;
    text-align: center;
  }

  .newsletter-content h3 {
    margin: 0 0 1rem 0;
    font-size: 1.5rem;
    font-weight: 600;
  }

  .newsletter-content p {
    margin: 0 0 1.5rem 0;
    opacity: 0.9;
    line-height: 1.6;
  }

  .newsletter-form {
    max-width: 400px;
    margin: 0 auto;
  }

  .form-group {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  .email-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    color: #374151;
  }

  .email-input:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
  }

  .submit-btn {
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
  }

  .submit-btn:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
  }

  .submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }

  .form-message {
    display: none;
    padding: 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
    margin-top: 1rem;
  }

  .form-message.success {
    background: rgba(16, 185, 129, 0.2);
    border: 1px solid rgba(16, 185, 129, 0.3);
    color: #ecfdf5;
  }

  .form-message.error {
    background: rgba(239, 68, 68, 0.2);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #fef2f2;
  }

  .privacy-note {
    margin: 1.5rem 0 0 0;
    font-size: 0.8rem;
    opacity: 0.8;
  }

  @media (max-width: 768px) {
    .newsletter-signup {
      padding: 1.5rem;
      margin: 1.5rem 0;
    }

    .newsletter-content h3 {
      font-size: 1.3rem;
    }

    .form-group {
      flex-direction: column;
    }

    .submit-btn {
      width: 100%;
    }
  }
</style>
