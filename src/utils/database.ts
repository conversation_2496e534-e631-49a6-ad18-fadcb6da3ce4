import mysql from 'mysql2/promise';

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'biagiotti_blog',
  port: parseInt(process.env.DB_PORT || '3306'),
  ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
  connectionLimit: 10,
  acquireTimeout: 60000,
  timeout: 60000,
};

// Create connection pool
let pool: mysql.Pool | null = null;

export function getPool() {
  if (!pool) {
    pool = mysql.createPool(dbConfig);
  }
  return pool;
}

// Database connection
export async function connectDB() {
  try {
    const connection = await getPool().getConnection();
    console.log('✅ Connected to MySQL database');
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ MySQL connection failed:', error);
    return false;
  }
}

// Newsletter subscription
export interface NewsletterSubscription {
  id?: number;
  email: string;
  name?: string;
  subscribed_at?: Date;
  status: 'active' | 'unsubscribed';
}

export async function subscribeToNewsletter(email: string, name?: string): Promise<boolean> {
  try {
    const pool = getPool();
    
    // Check if email already exists
    const [existing] = await pool.execute(
      'SELECT id, status FROM newsletter_subscriptions WHERE email = ?',
      [email]
    );
    
    if (Array.isArray(existing) && existing.length > 0) {
      const existingRecord = existing[0] as any;
      if (existingRecord.status === 'active') {
        return false; // Already subscribed
      } else {
        // Reactivate subscription
        await pool.execute(
          'UPDATE newsletter_subscriptions SET status = ?, name = ?, subscribed_at = NOW() WHERE email = ?',
          ['active', name || null, email]
        );
        return true;
      }
    }
    
    // Insert new subscription
    await pool.execute(
      'INSERT INTO newsletter_subscriptions (email, name, status, subscribed_at) VALUES (?, ?, ?, NOW())',
      [email, name || null, 'active']
    );
    
    return true;
  } catch (error) {
    console.error('Error subscribing to newsletter:', error);
    return false;
  }
}

export async function unsubscribeFromNewsletter(email: string): Promise<boolean> {
  try {
    const pool = getPool();
    
    const [result] = await pool.execute(
      'UPDATE newsletter_subscriptions SET status = ? WHERE email = ?',
      ['unsubscribed', email]
    );
    
    return (result as any).affectedRows > 0;
  } catch (error) {
    console.error('Error unsubscribing from newsletter:', error);
    return false;
  }
}

// Contact form submission
export interface ContactSubmission {
  id?: number;
  name: string;
  email: string;
  subject?: string;
  message: string;
  submitted_at?: Date;
  status: 'new' | 'read' | 'replied';
}

export async function saveContactSubmission(data: Omit<ContactSubmission, 'id' | 'submitted_at' | 'status'>): Promise<boolean> {
  try {
    const pool = getPool();
    
    await pool.execute(
      'INSERT INTO contact_submissions (name, email, subject, message, status, submitted_at) VALUES (?, ?, ?, ?, ?, NOW())',
      [data.name, data.email, data.subject || null, data.message, 'new']
    );
    
    return true;
  } catch (error) {
    console.error('Error saving contact submission:', error);
    return false;
  }
}

// Blog comments (if needed)
export interface BlogComment {
  id?: number;
  post_slug: string;
  author_name: string;
  author_email: string;
  content: string;
  status: 'pending' | 'approved' | 'rejected';
  created_at?: Date;
}

export async function saveComment(comment: Omit<BlogComment, 'id' | 'created_at'>): Promise<boolean> {
  try {
    const pool = getPool();
    
    await pool.execute(
      'INSERT INTO blog_comments (post_slug, author_name, author_email, content, status, created_at) VALUES (?, ?, ?, ?, ?, NOW())',
      [comment.post_slug, comment.author_name, comment.author_email, comment.content, comment.status]
    );
    
    return true;
  } catch (error) {
    console.error('Error saving comment:', error);
    return false;
  }
}

export async function getApprovedComments(postSlug: string): Promise<BlogComment[]> {
  try {
    const pool = getPool();
    
    const [rows] = await pool.execute(
      'SELECT * FROM blog_comments WHERE post_slug = ? AND status = ? ORDER BY created_at ASC',
      [postSlug, 'approved']
    );
    
    return rows as BlogComment[];
  } catch (error) {
    console.error('Error fetching comments:', error);
    return [];
  }
}

// Initialize database tables
export async function initializeDatabase(): Promise<boolean> {
  try {
    const pool = getPool();
    
    // Create newsletter_subscriptions table
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS newsletter_subscriptions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(255) NOT NULL UNIQUE,
        name VARCHAR(255),
        status ENUM('active', 'unsubscribed') DEFAULT 'active',
        subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    
    // Create contact_submissions table
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS contact_submissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        subject VARCHAR(500),
        message TEXT NOT NULL,
        status ENUM('new', 'read', 'replied') DEFAULT 'new',
        submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_status (status),
        INDEX idx_submitted_at (submitted_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    
    // Create blog_comments table
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS blog_comments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        post_slug VARCHAR(255) NOT NULL,
        author_name VARCHAR(255) NOT NULL,
        author_email VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_post_slug (post_slug),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    
    console.log('✅ Database tables initialized successfully');
    return true;
  } catch (error) {
    console.error('❌ Error initializing database:', error);
    return false;
  }
}
