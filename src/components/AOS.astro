---
// AOS Component for animations
---

<!-- AOS CSS from CDN -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

<!-- AOS JavaScript from CDN -->
<script is:inline src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

<script is:inline>
  // Initialize AOS when the page loads
  function initAOS() {
    if (typeof AOS !== 'undefined') {
      AOS.init({
        duration: 600,
        easing: 'ease-in-out',
        once: true,
        mirror: false,
        offset: 100,
        delay: 0,
      });
    }
  }

  // Initialize on DOM ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initAOS);
  } else {
    initAOS();
  }

  // Refresh AOS on navigation (for SPA behavior)
  document.addEventListener('astro:page-load', function() {
    if (typeof AOS !== 'undefined') {
      AOS.refresh();
    }
  });

  // Also refresh on astro:after-swap for view transitions
  document.addEventListener('astro:after-swap', function() {
    if (typeof AOS !== 'undefined') {
      AOS.refresh();
    }
  });
</script>
