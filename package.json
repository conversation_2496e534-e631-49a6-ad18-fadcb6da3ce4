{"name": "@example/blog", "type": "module", "version": "0.0.1", "private": true, "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "setup-strapi": "node scripts/setup.js", "sync-blog": "node scripts/strapi-sync.js sync", "sync-blog-single": "node scripts/strapi-sync.js sync", "list-articles": "node scripts/strapi-sync.js list", "manage-blog": "node scripts/blog-manager.js interactive", "blog-list": "node scripts/blog-manager.js list", "blog-publish": "node scripts/blog-manager.js publish", "blog-publish-all": "node scripts/blog-manager.js publish all", "blog-schedule": "node scripts/blog-manager.js schedule", "blog-auto-publish": "node scripts/blog-manager.js auto-publish", "auto-publisher": "node scripts/auto-publisher.js run", "auto-publisher-stats": "node scripts/auto-publisher.js stats", "auto-publisher-daemon": "node scripts/auto-publisher.js daemon"}, "dependencies": {"@astrojs/mdx": "^3.1.7", "@astrojs/node": "^8.3.4", "@astrojs/rss": "^4.0.7", "@astrojs/sitemap": "^3.2.0", "@supabase/supabase-js": "^2.49.8", "aos": "^2.3.4", "astro": "^4.15.11", "dotenv": "^16.5.0", "mysql2": "^3.14.1", "node-fetch": "^3.3.2", "nodemailer": "^7.0.3"}, "devDependencies": {"@types/nodemailer": "^6.4.17"}}