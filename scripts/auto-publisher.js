#!/usr/bin/env node

import BlogManager from './blog-manager.js';
import { getCollection } from 'astro:content';
import { getScheduledPosts, getPostsReadyToPublish } from '../src/utils/blog-filters.js';

/**
 * Script per la pubblicazione automatica degli articoli programmati
 * Può essere eseguito manualmente o tramite cron job
 */

class AutoPublisher {
  constructor() {
    this.manager = new BlogManager();
  }

  async close() {
    await this.manager.close();
  }

  /**
   * Controlla e pubblica gli articoli pronti
   */
  async checkAndPublish() {
    try {
      console.log('🔍 Controllo articoli programmati...');
      
      const result = await this.manager.publishScheduledPosts();
      
      if (result.published > 0) {
        console.log(`🎉 Pubblicati ${result.published} articoli!`);
        
        // Opzionale: invia notifica (email, webhook, etc.)
        await this.sendNotification(result);
      } else {
        console.log('📭 Nessun articolo da pubblicare al momento');
      }
      
      return result;
      
    } catch (error) {
      console.error('❌ Errore durante la pubblicazione automatica:', error.message);
      throw error;
    }
  }

  /**
   * Mostra statistiche degli articoli programmati
   */
  async showScheduledStats() {
    try {
      // Nota: questo richiede che Astro sia configurato, 
      // per ora usiamo il blog manager
      const articles = await this.manager.listArticles();
      const now = new Date();
      
      const scheduled = articles.filter(article => {
        return article.autoPublish && 
               article.scheduledDate && 
               new Date(article.scheduledDate) > now;
      });
      
      const ready = articles.filter(article => {
        return article.autoPublish && 
               article.scheduledDate && 
               new Date(article.scheduledDate) <= now &&
               !article.published;
      });
      
      console.log('\n📊 Statistiche Programmazione:');
      console.log(`⏰ Articoli programmati: ${scheduled.length}`);
      console.log(`🚀 Pronti per pubblicazione: ${ready.length}`);
      
      if (scheduled.length > 0) {
        console.log('\n📅 Prossime pubblicazioni:');
        scheduled
          .sort((a, b) => new Date(a.scheduledDate) - new Date(b.scheduledDate))
          .slice(0, 5)
          .forEach(article => {
            const date = new Date(article.scheduledDate);
            console.log(`  • ${article.title}`);
            console.log(`    📅 ${date.toLocaleDateString('it-IT')} alle ${date.toLocaleTimeString('it-IT', { hour: '2-digit', minute: '2-digit' })}`);
          });
      }
      
      if (ready.length > 0) {
        console.log('\n🚀 Pronti ora:');
        ready.forEach(article => {
          console.log(`  • ${article.title}`);
        });
      }
      
    } catch (error) {
      console.error('❌ Errore nel recupero delle statistiche:', error.message);
    }
  }

  /**
   * Invia notifica dopo la pubblicazione (opzionale)
   */
  async sendNotification(result) {
    // Implementa qui le tue notifiche personalizzate
    // Esempi: email, webhook, Slack, Discord, etc.
    
    const webhookUrl = process.env.BLOG_WEBHOOK_URL;
    if (webhookUrl) {
      try {
        await fetch(webhookUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            text: `🎉 Blog Auto-Publisher: Pubblicati ${result.published} articoli!`,
            published: result.published,
            total: result.total,
            timestamp: new Date().toISOString()
          })
        });
        console.log('📬 Notifica inviata');
      } catch (error) {
        console.warn('⚠️  Errore nell\'invio della notifica:', error.message);
      }
    }
  }

  /**
   * Modalità daemon - controlla periodicamente
   */
  async daemon(intervalMinutes = 60) {
    console.log(`🤖 Modalità daemon avviata - controllo ogni ${intervalMinutes} minuti`);
    console.log('Premi Ctrl+C per fermare\n');
    
    const interval = intervalMinutes * 60 * 1000;
    
    // Primo controllo immediato
    await this.checkAndPublish();
    
    // Controlli periodici
    const timer = setInterval(async () => {
      try {
        console.log(`\n⏰ ${new Date().toLocaleString('it-IT')} - Controllo programmato`);
        await this.checkAndPublish();
      } catch (error) {
        console.error('❌ Errore nel controllo periodico:', error.message);
      }
    }, interval);
    
    // Gestione graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 Arresto daemon...');
      clearInterval(timer);
      this.close().then(() => {
        console.log('✅ Daemon arrestato');
        process.exit(0);
      });
    });
  }

  /**
   * Modalità dry-run - mostra cosa verrebbe pubblicato
   */
  async dryRun() {
    try {
      console.log('🔍 Modalità Dry Run - Simulazione pubblicazione\n');
      
      const articles = await this.manager.listArticles();
      const now = new Date();
      
      const readyPosts = articles.filter(article => {
        if (!article.autoPublish || !article.scheduledDate || article.published) {
          return false;
        }
        
        const scheduleTime = new Date(article.scheduledDate);
        return scheduleTime <= now;
      });

      if (readyPosts.length === 0) {
        console.log('📭 Nessun articolo pronto per la pubblicazione');
        return;
      }

      console.log(`🚀 ${readyPosts.length} articoli verrebbero pubblicati:`);
      readyPosts.forEach((post, index) => {
        const scheduleTime = new Date(post.scheduledDate);
        console.log(`  ${index + 1}. ${post.title}`);
        console.log(`     📅 Programmato per: ${scheduleTime.toLocaleDateString('it-IT')} ${scheduleTime.toLocaleTimeString('it-IT', { hour: '2-digit', minute: '2-digit' })}`);
        console.log(`     ⏱️  In ritardo di: ${Math.round((now - scheduleTime) / (1000 * 60))} minuti`);
      });
      
      console.log('\n💡 Per pubblicare realmente, esegui senza --dry-run');
      
    } catch (error) {
      console.error('❌ Errore nella simulazione:', error.message);
    }
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'help';
  
  const publisher = new AutoPublisher();
  
  try {
    switch (command) {
      case 'run':
        await publisher.checkAndPublish();
        break;
        
      case 'stats':
        await publisher.showScheduledStats();
        break;
        
      case 'daemon':
        const interval = parseInt(args[1]) || 60;
        await publisher.daemon(interval);
        break;
        
      case 'dry-run':
        await publisher.dryRun();
        break;
        
      case 'help':
      default:
        console.log(`
🤖 Auto Publisher - Pubblicazione Automatica Articoli Programmati

Comandi disponibili:
  run                     Controlla e pubblica articoli pronti (una volta)
  stats                   Mostra statistiche articoli programmati
  daemon [minuti]         Modalità daemon - controllo continuo (default: 60 min)
  dry-run                 Simula pubblicazione senza modificare nulla
  help                    Mostra questo messaggio

Esempi:
  node scripts/auto-publisher.js run
  node scripts/auto-publisher.js stats
  node scripts/auto-publisher.js daemon 30
  node scripts/auto-publisher.js dry-run

Configurazione Cron (Linux/Mac):
  # Controlla ogni ora
  0 * * * * cd /path/to/blog && node scripts/auto-publisher.js run

  # Controlla ogni 15 minuti
  */15 * * * * cd /path/to/blog && node scripts/auto-publisher.js run

Configurazione Task Scheduler (Windows):
  - Crea task che esegue: node scripts/auto-publisher.js run
  - Imposta trigger ogni 15-60 minuti

Notifiche:
  Imposta BLOG_WEBHOOK_URL per ricevere notifiche via webhook:
  export BLOG_WEBHOOK_URL="https://hooks.slack.com/services/..."

Nota: Gli articoli programmati vengono pubblicati automaticamente
      quando la data/ora programmata è raggiunta o superata.
        `);
        break;
    }
  } finally {
    await publisher.close();
  }
}

// Esegui solo se chiamato direttamente
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export default AutoPublisher;
