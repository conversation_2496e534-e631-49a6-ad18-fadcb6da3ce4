---
interface Props {
  type: 'article' | 'website' | 'blog';
  title: string;
  description: string;
  url: string;
  image?: string;
  author?: string;
  publishDate?: Date;
  modifiedDate?: Date;
  category?: string;
  tags?: string[];
}

const { 
  type, 
  title, 
  description, 
  url, 
  image, 
  author = '<PERSON>',
  publishDate,
  modifiedDate,
  category,
  tags = []
} = Astro.props;

const baseUrl = Astro.site?.toString() || 'https://example.com';
const fullUrl = new URL(url, baseUrl).toString();
const imageUrl = image ? new URL(image, baseUrl).toString() : undefined;

let schema: any = {
  "@context": "https://schema.org",
  "@type": type === 'article' ? 'Article' : type === 'blog' ? 'Blog' : 'WebSite',
  "name": title,
  "headline": title,
  "description": description,
  "url": fullUrl,
  "inLanguage": "it-IT"
};

if (type === 'article') {
  schema = {
    ...schema,
    "@type": "Article",
    "author": {
      "@type": "Person",
      "name": author,
      "url": baseUrl
    },
    "publisher": {
      "@type": "Organization",
      "name": "<PERSON> <PERSON>iagiotti Blog",
      "url": baseUrl,
      "logo": {
        "@type": "ImageObject",
        "url": new URL('/favicon.svg', baseUrl).toString()
      }
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": fullUrl
    }
  };

  if (publishDate) {
    schema.datePublished = publishDate.toISOString();
  }

  if (modifiedDate) {
    schema.dateModified = modifiedDate.toISOString();
  }

  if (imageUrl) {
    schema.image = {
      "@type": "ImageObject",
      "url": imageUrl,
      "width": 1200,
      "height": 630
    };
  }

  if (category) {
    schema.articleSection = category;
  }

  if (tags.length > 0) {
    schema.keywords = tags.join(', ');
  }
}

if (type === 'blog') {
  schema = {
    ...schema,
    "@type": "Blog",
    "author": {
      "@type": "Person",
      "name": author,
      "url": baseUrl
    },
    "publisher": {
      "@type": "Organization",
      "name": "Marco Biagiotti Blog",
      "url": baseUrl
    }
  };
}

if (type === 'website') {
  schema = {
    ...schema,
    "@type": "WebSite",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/search?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    }
  };
}
---

<script type="application/ld+json" set:html={JSON.stringify(schema)} />

<!-- Additional meta tags for better SEO -->
{type === 'article' && (
  <>
    <meta property="article:author" content={author} />
    {publishDate && <meta property="article:published_time" content={publishDate.toISOString()} />}
    {modifiedDate && <meta property="article:modified_time" content={modifiedDate.toISOString()} />}
    {category && <meta property="article:section" content={category} />}
    {tags.map(tag => <meta property="article:tag" content={tag} />)}
  </>
)}

<!-- Breadcrumb Schema for articles -->
{type === 'article' && (
  <script type="application/ld+json" set:html={JSON.stringify({
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": baseUrl
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Blog",
        "item": new URL('/blog', baseUrl).toString()
      },
      {
        "@type": "ListItem",
        "position": 3,
        "name": title,
        "item": fullUrl
      }
    ]
  })} />
)}

<!-- FAQ Schema if the article contains FAQ-like content -->
{type === 'article' && (
  <script type="application/ld+json" set:html={JSON.stringify({
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": []
  })} />
)}
