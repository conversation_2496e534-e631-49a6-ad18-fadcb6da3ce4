---
import CategoryTag from './CategoryTag.astro';

interface Props {
  categories: string[];
  tags: string[];
  currentCategory?: string;
  currentTag?: string;
}

const { categories, tags, currentCategory, currentTag } = Astro.props;
---

<div class="blog-filters">
  <div class="search-section">
    <div class="search-container">
      <input
        type="text"
        id="search-input"
        placeholder="Cerca articoli..."
        class="search-input"
      />
      <button type="button" id="clear-search" class="clear-btn" style="display: none;">✕</button>
    </div>

    <!-- Search Results Summary -->
    <div id="search-results-summary" class="search-results-summary" style="display: none;">
      <div class="results-info">
        <span id="results-count">0</span>
        <span id="results-text">risultati trovati</span>
        <button type="button" id="scroll-to-results" class="scroll-to-results-btn">
          <i class="bi bi-arrow-down"></i>
          Vai ai risultati
        </button>
      </div>
    </div>
  </div>

  <div class="filters-section">
    <div class="filter-group">
      <h3>Categorie</h3>
      <div class="filter-items">
        <a href="/blog" class={`filter-item ${!currentCategory ? 'active' : ''}`}>
          Tutte
        </a>
        {categories.map(category => (
          <CategoryTag 
            name={category} 
            type="category" 
            size="small"
          />
        ))}
      </div>
    </div>

    <div class="filter-group">
      <h3>Tag</h3>
      <div class="filter-items">
        <a href="/blog" class={`filter-item ${!currentTag ? 'active' : ''}`}>
          Tutti
        </a>
        {tags.slice(0, 10).map(tag => (
          <CategoryTag 
            name={tag} 
            type="tag" 
            size="small"
          />
        ))}
        {tags.length > 10 && (
          <button type="button" id="show-more-tags" class="show-more-btn">
            +{tags.length - 10} altri
          </button>
        )}
      </div>
    </div>
  </div>
</div>

<script>
  // Search functionality
  const searchInput = document.getElementById('search-input') as HTMLInputElement;
  const clearBtn = document.getElementById('clear-search') as HTMLButtonElement;
  const resultsSummary = document.getElementById('search-results-summary') as HTMLElement;
  const resultsCount = document.getElementById('results-count') as HTMLElement;
  const resultsText = document.getElementById('results-text') as HTMLElement;
  const scrollToResultsBtn = document.getElementById('scroll-to-results') as HTMLButtonElement;
  const posts = document.querySelectorAll('.blog-post-item');

  function performSearch() {
    const query = searchInput.value.toLowerCase().trim();

    if (query === '') {
      // Reset: mostra tutti i post
      posts.forEach(post => {
        (post as HTMLElement).style.display = '';
      });
      clearBtn.style.display = 'none';
      resultsSummary.style.display = 'none';
      return;
    }

    clearBtn.style.display = 'block';
    let visibleCount = 0;

    posts.forEach(post => {
      const title = post.querySelector('.post-title')?.textContent?.toLowerCase() || '';
      const description = post.querySelector('.post-description')?.textContent?.toLowerCase() || '';
      const category = post.querySelector('.category-tag')?.textContent?.toLowerCase() || '';
      const tags = Array.from(post.querySelectorAll('.tag-item')).map(tag =>
        tag.textContent?.toLowerCase() || ''
      ).join(' ');

      const matches = title.includes(query) ||
                     description.includes(query) ||
                     category.includes(query) ||
                     tags.includes(query);

      if (matches) {
        (post as HTMLElement).style.display = '';
        visibleCount++;
      } else {
        (post as HTMLElement).style.display = 'none';
      }
    });

    // Aggiorna il summary dei risultati
    updateResultsSummary(visibleCount, query);
  }

  function updateResultsSummary(count: number, query: string) {
    resultsCount.textContent = count.toString();

    if (count === 0) {
      resultsText.textContent = `risultati trovati per "${query}"`;
      resultsSummary.className = 'search-results-summary no-results';
    } else if (count === 1) {
      resultsText.textContent = `risultato trovato per "${query}"`;
      resultsSummary.className = 'search-results-summary has-results';
    } else {
      resultsText.textContent = `risultati trovati per "${query}"`;
      resultsSummary.className = 'search-results-summary has-results';
    }

    resultsSummary.style.display = 'block';
  }

  function scrollToResults() {
    const allPostsSection = document.getElementById('all-posts');
    if (allPostsSection) {
      allPostsSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });

      // Aggiungi un piccolo highlight temporaneo
      allPostsSection.style.transition = 'background-color 0.3s ease';
      allPostsSection.style.backgroundColor = 'rgba(var(--theme-color-rgb), 0.05)';

      setTimeout(() => {
        allPostsSection.style.backgroundColor = '';
      }, 1000);
    }
  }

  searchInput?.addEventListener('input', performSearch);

  clearBtn?.addEventListener('click', () => {
    searchInput.value = '';
    performSearch();
    searchInput.focus();
  });

  scrollToResultsBtn?.addEventListener('click', scrollToResults);

  // Show more tags functionality
  const showMoreBtn = document.getElementById('show-more-tags');
  showMoreBtn?.addEventListener('click', () => {
    // This would expand to show all tags - implementation depends on your needs
    console.log('Show more tags clicked');
  });
</script>

<style>
  .blog-filters {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .search-section {
    margin-bottom: 1.5rem;
  }

  .search-container {
    position: relative;
    max-width: 400px;
  }

  .search-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s ease;
  }

  .search-input:focus {
    outline: none;
    border-color: var(--accent);
  }

  .clear-btn {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #64748b;
    padding: 0.25rem;
  }

  .clear-btn:hover {
    color: #ef4444;
  }

  /* Search Results Summary */
  .search-results-summary {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid var(--theme-color);
    background: linear-gradient(135deg, rgba(var(--theme-color-rgb), 0.05) 0%, rgba(var(--theme-color-rgb), 0.02) 100%);
    animation: slideDown 0.3s ease-out;
  }

  .search-results-summary.no-results {
    border-left-color: #f59e0b;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(245, 158, 11, 0.02) 100%);
  }

  .results-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  #results-count {
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--theme-color);
  }

  .no-results #results-count {
    color: #f59e0b;
  }

  #results-text {
    color: var(--default-color);
    font-size: 0.95rem;
  }

  .scroll-to-results-btn {
    background: var(--theme-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    margin-left: auto;
  }

  .scroll-to-results-btn:hover {
    background: color-mix(in srgb, var(--theme-color), black 10%);
    transform: translateY(-1px);
  }

  .scroll-to-results-btn i {
    font-size: 0.875rem;
  }

  .no-results .scroll-to-results-btn {
    background: #f59e0b;
  }

  .no-results .scroll-to-results-btn:hover {
    background: color-mix(in srgb, #f59e0b, black 10%);
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .filters-section {
    display: grid;
    gap: 1.5rem;
  }

  .filter-group h3 {
    margin: 0 0 0.75rem 0;
    font-size: 1.1rem;
    color: rgb(var(--black));
  }

  .filter-items {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
  }

  .filter-item {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    background: #f1f5f9;
    color: #475569;
    transition: all 0.2s ease;
  }

  .filter-item:hover,
  .filter-item.active {
    background: var(--accent);
    color: white;
  }

  .show-more-btn {
    background: #f1f5f9;
    border: none;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    cursor: pointer;
    color: #64748b;
    transition: all 0.2s ease;
  }

  .show-more-btn:hover {
    background: #e2e8f0;
  }

  @media (max-width: 768px) {
    .blog-filters {
      padding: 1rem;
    }

    .filters-section {
      gap: 1rem;
    }

    .search-results-summary {
      padding: 0.75rem;
    }

    .results-info {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.75rem;
    }

    .scroll-to-results-btn {
      margin-left: 0;
      align-self: stretch;
      justify-content: center;
    }

    #results-count {
      font-size: 1rem;
    }

    #results-text {
      font-size: 0.875rem;
    }
  }
</style>
