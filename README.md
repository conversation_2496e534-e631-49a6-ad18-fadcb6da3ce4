# 🚀 <PERSON> Blog

Blog personale di Marco Biagiotti costruito con Astro, con sistema completo di newsletter, contatti e privacy policy.

## ✨ Funzionalità

### 🎯 Core Features
- ✅ **Blog completo** con sistema di categorizzazione e tag
- ✅ **Ricerca intelligente** con feedback visivo e contatore risultati
- ✅ **Caricamento progressivo** articoli (infinite scroll)
- ✅ **Menu mobile professionale** con modale a tutta pagina
- ✅ **SEO ottimizzato** con meta tags e Schema markup
- ✅ **Design responsive** perfetto su tutti i dispositivi
- ✅ **Sistema commenti** con Utterances (GitHub Issues)
- ✅ **Newsletter MySQL** con email automatiche
- ✅ **Form contatti** con notifiche SMTP
- ✅ **Privacy Policy** conforme GDPR
- ✅ **Pagine dinamiche** per categorie e tag
- ✅ **Performance ottimizzate** con Astro
- ✅ **Back to Top** floating button per navigazione rapida

### 🎨 UI/UX
- ✅ **Design coerente** con tema verde personalizzato
- ✅ **Hamburger menu animato** per mobile
- ✅ **Accordion funzionanti** per sezioni collassabili
- ✅ **Slider infinito** per loghi clienti
- ✅ **Hover states** ottimizzati per tutti i pulsanti
- ✅ **Logo perfetto** senza distorsioni
- ✅ **Footer professionale** con informazioni legali
- ✅ **Privacy modal** responsive e accessibile

### 🗄️ Backend & Database
- ✅ **MySQL database** per newsletter e contatti
- ✅ **API REST hybrid** (server-side + static)
- ✅ **SMTP email** con template professionali
- ✅ **Validazione dati** completa e gestione errori
- ✅ **Schema ottimizzato** con indexes e triggers
- ✅ **Architettura scalabile** per alto traffico
- ✅ **Logging avanzato** per debug e monitoring

## 🛠️ Tecnologie Utilizzate

- **Frontend**: Astro 4.x, TypeScript, CSS moderno
- **Architecture**: Hybrid (Static + Server-side)
- **Database**: MySQL 5.7+ con schema ottimizzato
- **Email**: SMTP con nodemailer e template HTML
- **Styling**: CSS Variables, Bootstrap Icons, Responsive Design
- **API**: Astro API routes hybrid con validazione
- **Adapter**: Node.js standalone per production
- **Commenti**: Utterances (GitHub Issues)
- **Analytics**: Google Analytics/Plausible ready
- **Deployment**: Vercel/Netlify/VPS ready

## 🚀 Project Structure

Inside of your Astro project, you'll see the following folders and files:

```text
├── public/
├── src/
│   ├── components/
│   ├── content/
│   ├── layouts/
│   └── pages/
├── astro.config.mjs
├── README.md
├── package.json
└── tsconfig.json
```

Astro looks for `.astro` or `.md` files in the `src/pages/` directory. Each page is exposed as a route based on its file name.

There's nothing special about `src/components/`, but that's where we like to put any Astro/React/Vue/Svelte/Preact components.

The `src/content/` directory contains "collections" of related Markdown and MDX documents. Use `getCollection()` to retrieve posts from `src/content/blog/`, and type-check your frontmatter using an optional schema. See [Astro's Content Collections docs](https://docs.astro.build/en/guides/content-collections/) to learn more.

**If users upload Markdown files, copy them to workspace directory and use `getCollection()` to retrieve.**

Any static assets, like images, can be placed in the `public/` directory.

## 🚀 Quick Start

### 1. Installazione Base

```bash
# Clona il repository
git clone https://github.com/mbiagiottime/biagiotti-blog.git
cd biagiotti-blog

# Installa le dipendenze
npm install

# Avvia il server di sviluppo (solo frontend)
npm run dev
```

Il blog sarà disponibile su `http://localhost:4321`

### 2. Setup Completo con Database

Per utilizzare newsletter e form contatti:

#### A. Configura MySQL

```bash
# Crea il database
mysql -u root -p
CREATE DATABASE biagiotti_blog CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# Importa lo schema
mysql -u root -p biagiotti_blog < database/schema.sql
```

#### B. Configura le variabili d'ambiente

```bash
# Copia il file di esempio
cp .env.example .env

# Modifica .env con le tue credenziali
```

```env
# Database MySQL
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=biagiotti_blog

# SMTP Email
SMTP_HOST=mail.biagiotti.me
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password
```

#### C. Test del sistema

```bash
# Build per verificare che tutto funzioni
npm run build

# Avvia con funzionalità complete
npm run dev
```

### 3. Configurazioni Opzionali

#### Analytics
```env
GA_MEASUREMENT_ID=G-XXXXXXXXXX
PLAUSIBLE_DOMAIN=marcobiagiotti.me
```

#### Commenti Utterances
1. Installa l'app Utterances sul tuo repository GitHub
2. Abilita Issues nel repository
3. I commenti verranno gestiti automaticamente

## 🧞 Comandi Disponibili

| Comando                   | Azione                                           |
| :------------------------ | :----------------------------------------------- |
| `npm install`             | Installa le dipendenze                          |
| `npm run dev`             | Avvia il server di sviluppo                     |
| `npm run build`           | Build per produzione                             |
| `npm run preview`         | Anteprima del build                              |
| `npm run astro ...`       | Comandi CLI di Astro                             |

## 📊 Pannello Admin

Il pannello admin è disponibile su `/admin` con informazioni su come gestire i contenuti del blog.

## 🎨 Personalizzazione

### Colori e Tema
Modifica le variabili CSS in `src/styles/global.css`:

```css
:root {
  --theme-color: #00a99d;        /* Verde principale */
  --theme-color-rgb: 0, 169, 157;
  --heading-color: #2d3748;      /* Colore titoli */
  --default-color: #4a5568;      /* Colore testo */
}
```

### Configurazione Sito
Aggiorna le costanti in `src/consts.ts`:

```typescript
export const SITE_TITLE = 'Marco Biagiotti Blog';
export const SITE_DESCRIPTION = 'Blog personale di Marco Biagiotti';
```

### Logo e Branding
- **Logo**: Sostituisci `public/logo.png`
- **Favicon**: Sostituisci `public/favicon.svg`

### Database e Email
- **MySQL**: Configura credenziali in `.env`
- **SMTP**: Imposta server email per notifiche
- **Template**: Modifica template in `src/utils/email.ts`

### Privacy Policy
Aggiorna il contenuto in `src/components/Footer.astro`:
- Informazioni aziendali
- P.IVA e contatti
- Sezioni privacy policy

## 📝 Gestione Contenuti

### Aggiungere Nuovi Articoli
Crea file in `src/content/blog/`:

```markdown
---
title: 'Titolo del Post'
description: 'Descrizione breve per SEO'
pubDate: 2024-01-15
heroImage: '/blog-hero.jpg'
category: 'Tutorial'
tags: ['astro', 'web', 'development']
author: 'Marco Biagiotti'
featured: true
---

# Contenuto del post

Il tuo contenuto in Markdown...
```

### Categorie e Tag
- **Categorie**: Classificazione principale (Tutorial, Guide, News)
- **Tag**: Etichette specifiche (astro, javascript, seo)
- Le pagine vengono generate automaticamente su `/categories/` e `/tags/`

### Newsletter e Contatti
- **Newsletter**: Gli iscritti vengono salvati in MySQL
- **Contatti**: I messaggi vengono salvati e inoltrati via email
- **Gestione**: Accedi direttamente al database MySQL per amministrare

### Database Management
```sql
-- Visualizza iscritti newsletter
SELECT * FROM newsletter_subscriptions WHERE status = 'active';

-- Visualizza messaggi contatti
SELECT * FROM contact_submissions ORDER BY submitted_at DESC;

-- Statistiche
SELECT COUNT(*) as total_subscribers FROM newsletter_subscriptions WHERE status = 'active';
```

## 🚀 Deploy

### Frontend Only (Vercel/Netlify)
Per deploy solo frontend (senza newsletter/contatti):

```bash
npm run build
# Carica la cartella dist/
```

### Full Stack (VPS/Server)
Per deploy completo con database:

1. **Setup Server**:
   ```bash
   # Installa Node.js, MySQL, nginx
   sudo apt update
   sudo apt install nodejs npm mysql-server nginx
   ```

2. **Database**:
   ```bash
   # Configura MySQL
   sudo mysql_secure_installation
   mysql -u root -p < database/schema.sql
   ```

3. **Applicazione**:
   ```bash
   # Clone e build
   git clone https://github.com/mbiagiottime/biagiotti-blog.git
   cd biagiotti-blog
   npm install
   npm run build
   ```

4. **Configurazione**:
   ```bash
   # Configura .env con credenziali produzione
   cp .env.example .env
   # Modifica .env con dati reali
   ```

### Vercel con Database Esterno
```bash
npm i -g vercel
vercel
# Configura environment variables nel dashboard Vercel
```

## 📚 Documentazione Completa

Per guide dettagliate, consulta la cartella `docs/`:
- **[Setup MySQL](docs/MYSQL_SETUP.md)** - Configurazione database completa
- **[Migrazione](docs/MYSQL_MIGRATION_SUMMARY.md)** - Riepilogo modifiche
- **[Correzioni](docs/FIXES_SUMMARY.md)** - Tutte le correzioni effettuate
- **[Troubleshooting](docs/TROUBLESHOOTING.md)** - Risoluzione problemi
- **[Pulizia](docs/CLEANUP_SUMMARY.md)** - File rimossi e organizzazione

## 🆘 Supporto

### Documentazione
- 📖 [Documentazione Astro](https://docs.astro.build)
- 📚 [Guide MySQL](docs/MYSQL_SETUP.md)
- 🔧 [Troubleshooting](docs/TROUBLESHOOTING.md)

### Community
- 💬 [Discord Astro](https://astro.build/chat)
- 🐛 [Issues GitHub](https://github.com/mbiagiottime/biagiotti-blog/issues)

### Contatti
- 📧 **Email**: <EMAIL>
- 💼 **LinkedIn**: [linkedin.com/in/marcobiagiotti](https://www.linkedin.com/in/marcobiagiotti/)

## 🔧 Requisiti Sistema

### Sviluppo
- **Node.js** 18+
- **npm** 9+
- **MySQL** 5.7+ (per funzionalità complete)

### Produzione
- **Server** con Node.js
- **Database MySQL** configurato
- **SMTP Server** per email
- **SSL Certificate** raccomandato

## 📄 Licenza

MIT License

---

## 🎯 Caratteristiche Principali

- ✅ **Blog completo** con categorizzazione avanzata
- ✅ **Ricerca intelligente** con feedback visivo immediato
- ✅ **Caricamento progressivo** per performance ottimali
- ✅ **Newsletter MySQL** con email automatiche
- ✅ **Form contatti** con notifiche SMTP
- ✅ **Privacy Policy** conforme GDPR
- ✅ **Menu mobile** professionale con animazioni
- ✅ **Back to Top** floating button
- ✅ **SEO ottimizzato** per tutti i motori di ricerca
- ✅ **Performance** eccellenti con Astro hybrid
- ✅ **Design responsive** su tutti i dispositivi
- ✅ **Architettura scalabile** per alto traffico

**Sviluppato con ❤️ da Marco Biagiotti Innovation Manager**
