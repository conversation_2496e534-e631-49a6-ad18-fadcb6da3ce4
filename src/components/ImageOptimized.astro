---
interface Props {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  loading?: 'lazy' | 'eager';
  class?: string;
  sizes?: string;
  quality?: number;
}

const { 
  src, 
  alt, 
  width = 800, 
  height = 400, 
  loading = 'lazy',
  class: className = '',
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  quality = 80
} = Astro.props;

// Generate responsive image URLs (you can integrate with services like Cloudinary, ImageKit, etc.)
const generateResponsiveUrls = (src: string) => {
  // For demo, we'll use the original image
  // In production, integrate with image optimization service
  return {
    webp: src.replace(/\.(jpg|jpeg|png)$/i, '.webp'),
    avif: src.replace(/\.(jpg|jpeg|png)$/i, '.avif'),
    original: src
  };
};

const urls = generateResponsiveUrls(src);
---

<picture class={`responsive-image ${className}`}>
  <!-- AVIF format for modern browsers -->
  <source 
    srcset={urls.avif} 
    type="image/avif"
    sizes={sizes}
  />
  
  <!-- WebP format for most browsers -->
  <source 
    srcset={urls.webp} 
    type="image/webp"
    sizes={sizes}
  />
  
  <!-- Fallback to original format -->
  <img 
    src={src}
    alt={alt}
    width={width}
    height={height}
    loading={loading}
    decoding="async"
    sizes={sizes}
    style="aspect-ratio: auto;"
  />
</picture>

<style>
  .responsive-image {
    display: block;
    width: 100%;
    height: auto;
  }

  .responsive-image img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    transition: transform 0.3s ease;
  }

  .responsive-image:hover img {
    transform: scale(1.02);
  }

  /* Prevent layout shift */
  .responsive-image img[loading="lazy"] {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }

  @keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
  }
</style>
