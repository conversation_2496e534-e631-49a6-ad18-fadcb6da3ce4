---
import Layout from '../../layouts/Layout.astro';
import BlogFilters from '../../components/BlogFilters.astro';
import CategoryTag from '../../components/CategoryTag.astro';
import { getAllPosts, getAllCategories, getAllTags, getFeaturedPosts } from '../../utils/blog';
import FormattedDate from '../../components/FormattedDate.astro';

// Get all blog posts and metadata
const posts = await getAllPosts();
const categories = await getAllCategories();
const tags = await getAllTags();
const featuredPosts = await getFeaturedPosts();

const title = "Blog - <PERSON>";
const description = "Articoli e approfondimenti su tecnologia, innovazione e trasformazione digitale";
---

<Layout title={title} description={description}>
	<main class="blog-main">
		<div class="container">
			<!-- Hero Section -->
			<section class="blog-hero">
				<div class="hero-content">
					<h1>Blog di <PERSON></h1>
					<p class="lead">Articoli e approfondimenti su tecnologia, innovazione e trasformazione digitale</p>
				</div>
			</section>

			<!-- Filters Section -->
			<BlogFilters categories={categories} tags={tags} />

			<!-- Featured Posts Section -->
			{featuredPosts.length > 0 && (
				<section class="featured-section">
					<h2>Articoli in Evidenza</h2>
					<div class="featured-posts">
						{featuredPosts.slice(0, 2).map((post) => (
							<article class="featured-post">
								<a href={`/blog/${post.slug}/`}>
									{post.data.heroImage && <img src={post.data.heroImage} alt={post.data.title} />}
									<div class="featured-content">
										<h3>{post.data.title}</h3>
										<p>{post.data.description}</p>
										<div class="post-meta">
											<FormattedDate date={post.data.pubDate} />
											{post.data.category && (
												<CategoryTag name={post.data.category} type="category" size="small" />
											)}
										</div>
									</div>
								</a>
							</article>
						))}
					</div>
				</section>
			)}

			<!-- All Posts Section -->
			<section class="all-posts-section" id="all-posts">
				<h2>Tutti gli Articoli</h2>

				<!-- Search Results Summary -->
				<div id="search-results-summary" class="search-results-summary" style="display: none;">
					<div class="results-info">
						<span id="results-count">0</span>
						<span id="results-text">risultati trovati</span>
						<button type="button" id="scroll-to-results" class="scroll-to-results-btn">
							<i class="bi bi-arrow-down"></i>
							Vai ai risultati
						</button>
					</div>
				</div>

				<div class="posts-grid" id="posts-container">
					{posts.slice(0, 6).map((post) => (
						<article class="blog-post-item" data-post-title={post.data.title.toLowerCase()} data-post-description={post.data.description?.toLowerCase() || ''} data-post-category={post.data.category?.toLowerCase() || ''} data-post-tags={post.data.tags?.join(' ').toLowerCase() || ''}>
							<a href={`/blog/${post.slug}/`}>
								{post.data.heroImage && <img src={post.data.heroImage} alt={post.data.title} />}
								<div class="post-content">
									<h4 class="post-title">{post.data.title}</h4>
									<p class="post-description">{post.data.description}</p>
									<div class="post-meta">
										<FormattedDate date={post.data.pubDate} />
										{post.data.author && (
											<span class="author">di {post.data.author}</span>
										)}
									</div>
									<div class="post-tags">
										{post.data.category && (
											<CategoryTag name={post.data.category} type="category" size="small" />
										)}
										{post.data.tags?.slice(0, 3).map(tag => (
											<CategoryTag name={tag} type="tag" size="small" />
										))}
									</div>
								</div>
							</a>
						</article>
					))}
				</div>

				<!-- Load More Button -->
				{posts.length > 6 && (
					<div class="load-more-container">
						<button id="load-more-btn" class="load-more-btn">
							<i class="bi bi-plus-circle"></i>
							Carica altri articoli
							<span id="load-more-count">({posts.length - 6} rimanenti)</span>
						</button>
					</div>
				)}

				<!-- Loading Indicator -->
				<div id="loading-indicator" class="loading-indicator" style="display: none;">
					<div class="spinner"></div>
					<span>Caricamento articoli...</span>
				</div>
			</section>
		</div>
	</main>
</Layout>

<style>
	.blog-main {
		padding: 2rem 0;
		background: var(--background-color);
	}

	.container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 1rem;
	}

	/* Blog Hero Section */
	.blog-hero {
		text-align: center;
		padding: 4rem 0;
		background: linear-gradient(135deg, rgba(var(--theme-color-rgb), 0.1) 0%, rgba(var(--theme-color-rgb), 0.05) 100%);
		border-radius: 15px;
		margin-bottom: 3rem;
	}

	.hero-content h1 {
		font-size: 3rem;
		font-weight: 700;
		margin-bottom: 1rem;
		color: var(--heading-color);
	}

	.hero-content .lead {
		font-size: 1.2rem;
		color: var(--default-color);
		opacity: 0.8;
		max-width: 600px;
		margin: 0 auto;
	}

	/* Section Headings */
	h2 {
		font-size: 2rem;
		font-weight: 600;
		margin: 3rem 0 2rem 0;
		color: var(--heading-color);
		text-align: center;
	}

	/* Featured Posts Section */
	.featured-section {
		margin-bottom: 4rem;
	}

	.featured-posts {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
		gap: 2rem;
		margin-bottom: 2rem;
	}

	.featured-post {
		background: var(--surface-color);
		border-radius: 15px;
		overflow: hidden;
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
		transition: transform 0.3s ease, box-shadow 0.3s ease;
	}

	.featured-post:hover {
		transform: translateY(-10px);
		box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
	}

	.featured-post a {
		text-decoration: none;
		color: inherit;
		display: block;
	}

	.featured-post img {
		width: 100%;
		height: 250px;
		object-fit: cover;
	}

	.featured-content {
		padding: 2rem;
	}

	.featured-content h3 {
		margin: 0 0 1rem 0;
		font-size: 1.4rem;
		color: var(--heading-color);
		font-weight: 600;
		line-height: 1.3;
	}

	.featured-content p {
		margin: 0 0 1.5rem 0;
		color: var(--default-color);
		line-height: 1.6;
	}

	/* All Posts Section */
	.posts-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
		gap: 2rem;
	}

	.blog-post-item {
		background: var(--surface-color);
		border-radius: 15px;
		overflow: hidden;
		box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
		transition: transform 0.3s ease, box-shadow 0.3s ease;
	}

	.blog-post-item:hover {
		transform: translateY(-5px);
		box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
	}

	.blog-post-item a {
		text-decoration: none;
		color: inherit;
		display: block;
		height: 100%;
	}

	.blog-post-item img {
		width: 100%;
		height: 200px;
		object-fit: cover;
	}

	.post-content {
		padding: 1.5rem;
		display: flex;
		flex-direction: column;
		height: calc(100% - 200px);
	}

	.post-title {
		margin: 0 0 1rem 0;
		font-size: 1.2rem;
		color: var(--heading-color);
		line-height: 1.3;
		font-weight: 600;
		flex-grow: 1;
	}

	.post-description {
		margin: 0 0 1rem 0;
		color: var(--default-color);
		line-height: 1.5;
		font-size: 0.95rem;
	}

	.post-meta {
		display: flex;
		align-items: center;
		gap: 1rem;
		margin-bottom: 1rem;
		font-size: 0.875rem;
		color: var(--gray);
		flex-wrap: wrap;
	}

	.author {
		font-style: italic;
	}

	.post-tags {
		display: flex;
		flex-wrap: wrap;
		gap: 0.5rem;
		margin-top: auto;
	}

	/* Search Results Summary */
	.search-results-summary {
		margin: 2rem 0;
		padding: 1rem;
		border-radius: 8px;
		border-left: 4px solid var(--theme-color);
		background: linear-gradient(135deg, rgba(var(--theme-color-rgb), 0.05) 0%, rgba(var(--theme-color-rgb), 0.02) 100%);
		animation: slideDown 0.3s ease-out;
	}

	.search-results-summary.no-results {
		border-left-color: #f59e0b;
		background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(245, 158, 11, 0.02) 100%);
	}

	.results-info {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		flex-wrap: wrap;
	}

	#results-count {
		font-weight: 700;
		font-size: 1.1rem;
		color: var(--theme-color);
	}

	.no-results #results-count {
		color: #f59e0b;
	}

	#results-text {
		color: var(--default-color);
		font-size: 0.95rem;
	}

	.scroll-to-results-btn {
		background: var(--theme-color);
		color: white;
		border: none;
		padding: 0.5rem 1rem;
		border-radius: 6px;
		font-size: 0.875rem;
		font-weight: 500;
		cursor: pointer;
		display: flex;
		align-items: center;
		gap: 0.5rem;
		transition: all 0.2s ease;
		margin-left: auto;
	}

	.scroll-to-results-btn:hover {
		background: color-mix(in srgb, var(--theme-color), black 10%);
		transform: translateY(-1px);
	}

	.no-results .scroll-to-results-btn {
		background: #f59e0b;
	}

	.no-results .scroll-to-results-btn:hover {
		background: color-mix(in srgb, #f59e0b, black 10%);
	}

	/* Load More Button */
	.load-more-container {
		text-align: center;
		margin: 3rem 0;
	}

	.load-more-btn {
		background: var(--theme-color);
		color: white;
		border: none;
		padding: 1rem 2rem;
		border-radius: 8px;
		font-size: 1rem;
		font-weight: 600;
		cursor: pointer;
		display: inline-flex;
		align-items: center;
		gap: 0.5rem;
		transition: all 0.3s ease;
		box-shadow: 0 4px 15px rgba(var(--theme-color-rgb), 0.3);
	}

	.load-more-btn:hover {
		background: color-mix(in srgb, var(--theme-color), black 10%);
		transform: translateY(-2px);
		box-shadow: 0 8px 25px rgba(var(--theme-color-rgb), 0.4);
	}

	.load-more-btn:disabled {
		background: #ccc;
		cursor: not-allowed;
		transform: none;
		box-shadow: none;
	}

	#load-more-count {
		font-size: 0.875rem;
		opacity: 0.8;
	}

	/* Loading Indicator */
	.loading-indicator {
		text-align: center;
		padding: 2rem;
		color: var(--default-color);
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 1rem;
	}

	.spinner {
		width: 40px;
		height: 40px;
		border: 4px solid rgba(var(--theme-color-rgb), 0.1);
		border-left: 4px solid var(--theme-color);
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	@keyframes slideDown {
		from {
			opacity: 0;
			transform: translateY(-10px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	/* Hidden posts for progressive loading */
	.blog-post-item.hidden {
		display: none;
	}

	@media (max-width: 768px) {
		.hero-content h1 {
			font-size: 2.5rem;
		}

		.featured-posts {
			grid-template-columns: 1fr;
			gap: 1.5rem;
		}

		.posts-grid {
			grid-template-columns: 1fr;
			gap: 1.5rem;
		}

		.featured-content,
		.post-content {
			padding: 1.5rem;
		}

		h2 {
			font-size: 1.8rem;
		}

		.blog-hero {
			padding: 2rem 1rem;
		}

		/* Mobile responsive for new features */
		.search-results-summary {
			padding: 0.75rem;
		}

		.results-info {
			flex-direction: column;
			align-items: flex-start;
			gap: 0.75rem;
		}

		.scroll-to-results-btn {
			margin-left: 0;
			align-self: stretch;
			justify-content: center;
		}

		.load-more-btn {
			padding: 0.875rem 1.5rem;
			font-size: 0.95rem;
		}
	}
</style>

<script define:vars={{ allPostsData: posts.map(post => ({
	slug: post.slug,
	title: post.data.title,
	description: post.data.description || '',
	category: post.data.category || '',
	tags: post.data.tags?.join(' ') || '',
	heroImage: post.data.heroImage || '',
	pubDate: post.data.pubDate.toISOString(),
	author: post.data.author || ''
})) }}>
	// All posts data for progressive loading
	const allPosts = allPostsData;

	let currentlyLoaded = 6;
	let isSearchActive = false;

	// DOM elements
	const searchInput = document.getElementById('search-input');
	const clearBtn = document.getElementById('clear-search');
	const resultsSummary = document.getElementById('search-results-summary');
	const resultsCount = document.getElementById('results-count');
	const resultsText = document.getElementById('results-text');
	const scrollToResultsBtn = document.getElementById('scroll-to-results');
	const postsContainer = document.getElementById('posts-container');
	const loadMoreBtn = document.getElementById('load-more-btn');
	const loadMoreCount = document.getElementById('load-more-count');
	const loadingIndicator = document.getElementById('loading-indicator');

	// Search functionality
	function performSearch() {
		if (!searchInput) return;

		const query = searchInput.value.toLowerCase().trim();
		const posts = document.querySelectorAll('.blog-post-item');

		if (query === '') {
			// Reset: show all posts
			posts.forEach(post => {
				post.style.display = '';
			});
			if (clearBtn) clearBtn.style.display = 'none';
			if (resultsSummary) resultsSummary.style.display = 'none';
			isSearchActive = false;
			updateLoadMoreButton();
			return;
		}

		if (clearBtn) clearBtn.style.display = 'block';
		isSearchActive = true;
		let visibleCount = 0;

		posts.forEach(post => {
			const title = post.dataset.postTitle || '';
			const description = post.dataset.postDescription || '';
			const category = post.dataset.postCategory || '';
			const tags = post.dataset.postTags || '';

			const matches = title.includes(query) ||
						   description.includes(query) ||
						   category.includes(query) ||
						   tags.includes(query);

			if (matches) {
				post.style.display = '';
				visibleCount++;
			} else {
				post.style.display = 'none';
			}
		});

		// Update results summary
		updateResultsSummary(visibleCount, query);
		updateLoadMoreButton();
	}

	function updateResultsSummary(count, query) {
		if (!resultsSummary || !resultsCount || !resultsText) return;

		resultsCount.textContent = count.toString();

		if (count === 0) {
			resultsText.textContent = `risultati trovati per "${query}"`;
			resultsSummary.className = 'search-results-summary no-results';
		} else if (count === 1) {
			resultsText.textContent = `risultato trovato per "${query}"`;
			resultsSummary.className = 'search-results-summary has-results';
		} else {
			resultsText.textContent = `risultati trovati per "${query}"`;
			resultsSummary.className = 'search-results-summary has-results';
		}

		resultsSummary.style.display = 'block';
	}

	function scrollToResults() {
		const allPostsSection = document.getElementById('all-posts');
		if (allPostsSection) {
			allPostsSection.scrollIntoView({
				behavior: 'smooth',
				block: 'start'
			});

			// Highlight temporaneo
			allPostsSection.style.transition = 'background-color 0.3s ease';
			allPostsSection.style.backgroundColor = 'rgba(var(--theme-color-rgb), 0.05)';

			setTimeout(() => {
				allPostsSection.style.backgroundColor = '';
			}, 1000);
		}
	}

	// Progressive loading functionality
	function loadMorePosts() {
		if (!loadMoreBtn || !postsContainer || !loadingIndicator) return;

		// Show loading indicator
		loadMoreBtn.style.display = 'none';
		loadingIndicator.style.display = 'flex';

		// Simulate loading delay for better UX
		setTimeout(() => {
			const postsToLoad = Math.min(6, allPosts.length - currentlyLoaded);
			const newPosts = allPosts.slice(currentlyLoaded, currentlyLoaded + postsToLoad);

			newPosts.forEach(post => {
				const postElement = createPostElement(post);
				postsContainer.appendChild(postElement);
			});

			currentlyLoaded += postsToLoad;

			// Hide loading indicator
			loadingIndicator.style.display = 'none';

			// Update load more button
			updateLoadMoreButton();

			// Animate new posts
			const newElements = postsContainer.querySelectorAll('.blog-post-item:nth-last-child(-n+' + postsToLoad + ')');
			newElements.forEach((el, index) => {
				el.style.opacity = '0';
				el.style.transform = 'translateY(20px)';
				setTimeout(() => {
					el.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
					el.style.opacity = '1';
					el.style.transform = 'translateY(0)';
				}, index * 100);
			});
		}, 800);
	}

	function createPostElement(post) {
		const article = document.createElement('article');
		article.className = 'blog-post-item';
		article.dataset.postTitle = post.title.toLowerCase();
		article.dataset.postDescription = post.description.toLowerCase();
		article.dataset.postCategory = post.category.toLowerCase();
		article.dataset.postTags = post.tags.toLowerCase();

		article.innerHTML = `
			<a href="/blog/${post.slug}/">
				${post.heroImage ? `<img src="${post.heroImage}" alt="${post.title}" />` : ''}
				<div class="post-content">
					<h4 class="post-title">${post.title}</h4>
					<p class="post-description">${post.description}</p>
					<div class="post-meta">
						<time datetime="${post.pubDate}">${new Date(post.pubDate).toLocaleDateString('it-IT')}</time>
						${post.author ? `<span class="author">di ${post.author}</span>` : ''}
					</div>
					<div class="post-tags">
						${post.category ? `<span class="category-tag category small">${post.category}</span>` : ''}
						${post.tags ? post.tags.split(' ').slice(0, 3).map(tag =>
							`<span class="category-tag tag small">${tag}</span>`
						).join('') : ''}
					</div>
				</div>
			</a>
		`;

		return article;
	}

	function updateLoadMoreButton() {
		if (!loadMoreBtn || !loadMoreCount) return;

		if (isSearchActive) {
			// Hide load more during search
			loadMoreBtn.parentElement.style.display = 'none';
		} else {
			const remaining = allPosts.length - currentlyLoaded;
			if (remaining > 0) {
				loadMoreBtn.parentElement.style.display = 'block';
				loadMoreCount.textContent = `(${remaining} rimanenti)`;
			} else {
				loadMoreBtn.parentElement.style.display = 'none';
			}
		}
	}

	// Event listeners
	document.addEventListener('DOMContentLoaded', function() {
		if (searchInput) {
			searchInput.addEventListener('input', performSearch);
		}

		if (clearBtn) {
			clearBtn.addEventListener('click', () => {
				if (searchInput) {
					searchInput.value = '';
					performSearch();
					searchInput.focus();
				}
			});
		}

		if (scrollToResultsBtn) {
			scrollToResultsBtn.addEventListener('click', scrollToResults);
		}

		if (loadMoreBtn) {
			loadMoreBtn.addEventListener('click', loadMorePosts);
		}

		// Initialize load more button state
		updateLoadMoreButton();
	});
</script>