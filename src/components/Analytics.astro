---
interface Props {
  postId?: string;
  trackPageView?: boolean;
}

const { postId, trackPageView = true } = Astro.props;
---

<!-- Google Analytics (replace with your GA4 measurement ID) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>

<!-- Custom Analytics -->
<script define:vars={{ postId, trackPageView }}>
  // Simple analytics tracking
  class BlogAnalytics {
    constructor() {
      this.sessionId = this.generateSessionId();
      this.startTime = Date.now();
    }

    generateSessionId() {
      return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    }

    async trackEvent(eventType, data = {}) {
      const eventData = {
        event_type: eventType,
        session_id: this.sessionId,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        referrer: document.referrer,
        user_agent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        ...data
      };

      // Send to your analytics endpoint
      try {
        await fetch('/api/analytics', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(eventData)
        });
      } catch (error) {
        console.error('Analytics error:', error);
      }

      // Also store locally for offline analysis
      this.storeLocalAnalytics(eventData);
    }

    storeLocalAnalytics(eventData) {
      try {
        const stored = JSON.parse(localStorage.getItem('blog_analytics') || '[]');
        stored.push(eventData);
        
        // Keep only last 100 events
        if (stored.length > 100) {
          stored.splice(0, stored.length - 100);
        }
        
        localStorage.setItem('blog_analytics', JSON.stringify(stored));
      } catch (error) {
        console.error('Local analytics storage error:', error);
      }
    }

    trackPageView() {
      this.trackEvent('page_view', {
        post_id: postId,
        page_type: postId ? 'blog_post' : 'page'
      });
    }

    trackTimeOnPage() {
      const timeSpent = Math.round((Date.now() - this.startTime) / 1000);
      this.trackEvent('time_on_page', {
        post_id: postId,
        time_spent_seconds: timeSpent
      });
    }

    trackScroll() {
      let maxScroll = 0;
      let scrollTimeout;

      const handleScroll = () => {
        const scrollPercent = Math.round(
          (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
        );
        
        if (scrollPercent > maxScroll) {
          maxScroll = scrollPercent;
        }

        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(() => {
          this.trackEvent('scroll_depth', {
            post_id: postId,
            max_scroll_percent: maxScroll,
            current_scroll_percent: scrollPercent
          });
        }, 1000);
      };

      window.addEventListener('scroll', handleScroll, { passive: true });
    }

    trackClicks() {
      document.addEventListener('click', (event) => {
        const target = event.target as HTMLElement;
        
        // Track link clicks
        if (target.tagName === 'A' || target.closest('a')) {
          const link = target.closest('a') as HTMLAnchorElement;
          this.trackEvent('link_click', {
            post_id: postId,
            link_url: link.href,
            link_text: link.textContent?.trim(),
            is_external: !link.href.startsWith(window.location.origin)
          });
        }

        // Track social share clicks
        if (target.closest('.share-btn')) {
          const shareBtn = target.closest('.share-btn') as HTMLElement;
          const platform = shareBtn.className.split(' ').find(c => 
            ['twitter', 'facebook', 'linkedin', 'whatsapp', 'email', 'copy'].includes(c)
          );
          
          this.trackEvent('social_share', {
            post_id: postId,
            platform: platform || 'unknown'
          });
        }
      });
    }

    init() {
      // Track page view
      if (trackPageView) {
        this.trackPageView();
      }

      // Track scroll behavior
      this.trackScroll();

      // Track clicks
      this.trackClicks();

      // Track time on page when user leaves
      window.addEventListener('beforeunload', () => {
        this.trackTimeOnPage();
      });

      // Track visibility changes (tab switching)
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'hidden') {
          this.trackTimeOnPage();
        }
      });
    }
  }

  // Initialize analytics
  const analytics = new BlogAnalytics();
  
  // Start tracking when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => analytics.init());
  } else {
    analytics.init();
  }

  // Make analytics available globally for manual tracking
  window.blogAnalytics = analytics;
</script>

<!-- Plausible Analytics (privacy-friendly alternative) -->
<!-- Uncomment and replace with your domain -->
<!--
<script defer data-domain="yourdomain.com" src="https://plausible.io/js/script.js"></script>
-->

<style>
  /* Hide analytics elements */
  .analytics-hidden {
    display: none !important;
  }
</style>
