---
import BaseHead from '../components/BaseHead.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';

interface Props {
	title: string;
	description?: string;
	image?: string;
}

const { title, description, image } = Astro.props;
---

<!doctype html>
<html lang="it">
	<head>
		<BaseHead title={title} description={description} image={image} />
		<!-- Google Fonts -->
		<link href="https://fonts.googleapis.com" rel="preconnect">
		<link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
		<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Raleway:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
	</head>
	<body class="index-page">
		<Header />
		<slot />
		<Footer />

		<!-- Bootstrap JavaScript -->
		<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
	</body>
</html>

<style is:global>
	/* Import unified global styles */
	@import url('/src/styles/global.css');

	html {
		font-family: var(--default-font);
		font-size: 16px;
		scroll-behavior: smooth;
	}

	body {
		margin: 0;
		background: var(--background-color);
		color: var(--default-color);
		line-height: 1.6;
		font-family: var(--default-font);
	}

	* {
		box-sizing: border-box;
	}

	/* Ensure main content doesn't have conflicting styles */
	main {
		width: 100%;
		max-width: none;
		margin: 0;
		padding: 0;
	}

	/* Override blog-specific main styles for unified layout */
	.main {
		width: 100%;
		max-width: none;
		margin: 0;
		padding: 0;
	}
</style>
