#!/usr/bin/env node

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import readline from 'readline';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class SetupWizard {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  async question(prompt) {
    return new Promise((resolve) => {
      this.rl.question(prompt, resolve);
    });
  }

  async close() {
    this.rl.close();
  }

  async checkStrapiConnection(baseUrl, token) {
    try {
      const response = await fetch(`${baseUrl}/api/articolos?pagination[limit]=1`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        return {
          success: true,
          articlesCount: data.meta?.pagination?.total || 0
        };
      } else {
        return {
          success: false,
          error: `HTTP ${response.status}: ${response.statusText}`
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  async createEnvFile(strapiUrl, token) {
    const envPath = path.join(__dirname, '..', '.env');
    const envContent = `# Strapi Configuration
STRAPI_BASE_URL=${strapiUrl}
STRAPI_API_TOKEN=${token}

# Environment
NODE_ENV=development
`;

    try {
      // Controlla se .env esiste già
      const exists = await fs.access(envPath).then(() => true).catch(() => false);
      
      if (exists) {
        const overwrite = await this.question('Il file .env esiste già. Vuoi sovrascriverlo? (y/N): ');
        if (overwrite.toLowerCase() !== 'y') {
          console.log('ℹ️  File .env non modificato. Aggiungi manualmente:');
          console.log(`STRAPI_BASE_URL=${strapiUrl}`);
          console.log(`STRAPI_API_TOKEN=${token}`);
          return;
        }
      }

      await fs.writeFile(envPath, envContent);
      console.log('✅ File .env creato con successo!');
    } catch (error) {
      console.error('❌ Errore nella creazione del file .env:', error.message);
      console.log('ℹ️  Aggiungi manualmente le variabili d\'ambiente:');
      console.log(`export STRAPI_BASE_URL="${strapiUrl}"`);
      console.log(`export STRAPI_API_TOKEN="${token}"`);
    }
  }

  async updatePackageJson() {
    const packagePath = path.join(__dirname, '..', 'package.json');
    
    try {
      const packageContent = await fs.readFile(packagePath, 'utf8');
      const packageJson = JSON.parse(packageContent);
      
      // Aggiungi script se non esistono
      if (!packageJson.scripts) {
        packageJson.scripts = {};
      }
      
      const scriptsToAdd = {
        'sync-blog': 'node scripts/strapi-sync.js sync',
        'sync-blog-single': 'node scripts/strapi-sync.js sync',
        'list-articles': 'node scripts/strapi-sync.js list'
      };
      
      let added = false;
      for (const [script, command] of Object.entries(scriptsToAdd)) {
        if (!packageJson.scripts[script]) {
          packageJson.scripts[script] = command;
          added = true;
        }
      }
      
      if (added) {
        await fs.writeFile(packagePath, JSON.stringify(packageJson, null, 2));
        console.log('✅ Script aggiunti a package.json!');
        console.log('   Ora puoi usare: npm run sync-blog');
      } else {
        console.log('ℹ️  Script già presenti in package.json');
      }
      
    } catch (error) {
      console.error('❌ Errore nell\'aggiornamento di package.json:', error.message);
    }
  }

  async createDirectories() {
    const dirs = [
      path.join(__dirname, '..', 'src', 'content', 'blog'),
      path.join(__dirname, '..', 'public', 'blog-images'),
      path.join(__dirname, '..', 'backups', 'blog')
    ];

    for (const dir of dirs) {
      try {
        await fs.mkdir(dir, { recursive: true });
        console.log(`✅ Directory creata: ${path.relative(path.join(__dirname, '..'), dir)}`);
      } catch (error) {
        console.error(`❌ Errore nella creazione di ${dir}:`, error.message);
      }
    }
  }

  async testSync(strapiUrl, token) {
    console.log('\n🧪 Test di sincronizzazione...');
    
    try {
      // Importa dinamicamente il modulo di sync
      const { default: StrapiSync } = await import('./strapi-sync.js');
      
      // Imposta temporaneamente il token e l'URL
      process.env.STRAPI_BASE_URL = strapiUrl;
      process.env.STRAPI_API_TOKEN = token;
      
      const sync = new StrapiSync();
      const articles = await sync.getArticles();
      
      if (articles.length > 0) {
        console.log(`✅ Test riuscito! Trovati ${articles.length} articoli.`);
        
        const testSync = await this.question('Vuoi fare un test di sincronizzazione del primo articolo? (y/N): ');
        if (testSync.toLowerCase() === 'y') {
          await sync.syncById(articles[0].id);
        }
      } else {
        console.log('⚠️  Nessun articolo trovato. Verifica la configurazione Strapi.');
      }
      
    } catch (error) {
      console.error('❌ Errore nel test:', error.message);
    }
  }

  async run() {
    console.log(`
🚀 Setup Wizard - Strapi Blog Sync Tool
=======================================

Questo wizard ti aiuterà a configurare la sincronizzazione tra il tuo Strapi CMS e il blog Astro.
`);

    try {
      // 1. Verifica URL Strapi
      console.log('1️⃣  Configurazione Strapi');
      const strapiUrl = await this.question('URL del tuo Strapi (default: https://contents.biagiotti.me): ') || 'https://contents.biagiotti.me';
      
      // 2. Richiedi token API
      console.log('\n2️⃣  Token API');
      console.log('Per ottenere il token API:');
      console.log(`   1. Vai su ${strapiUrl}/admin`);
      console.log('   2. Settings → API Tokens');
      console.log('   3. Create new API Token');
      console.log('   4. Tipo: Read-only, Durata: Unlimited');
      console.log('');
      
      const token = await this.question('Inserisci il tuo token API Strapi: ');
      
      if (!token) {
        console.log('❌ Token richiesto per continuare.');
        return;
      }

      // 3. Test connessione
      console.log('\n3️⃣  Test connessione...');
      const connectionTest = await this.checkStrapiConnection(strapiUrl, token);
      
      if (connectionTest.success) {
        console.log(`✅ Connessione riuscita! Trovati ${connectionTest.articlesCount} articoli.`);
      } else {
        console.log(`❌ Errore di connessione: ${connectionTest.error}`);
        const continueAnyway = await this.question('Vuoi continuare comunque? (y/N): ');
        if (continueAnyway.toLowerCase() !== 'y') {
          return;
        }
      }

      // 4. Crea file .env
      console.log('\n4️⃣  Configurazione ambiente...');
      await this.createEnvFile(strapiUrl, token);

      // 5. Crea directory
      console.log('\n5️⃣  Creazione directory...');
      await this.createDirectories();

      // 6. Aggiorna package.json
      console.log('\n6️⃣  Aggiornamento package.json...');
      await this.updatePackageJson();

      // 7. Test finale
      if (connectionTest.success) {
        console.log('\n7️⃣  Test finale...');
        await this.testSync(strapiUrl, token);
      }

      // 8. Riepilogo
      console.log(`
🎉 Setup completato!

Prossimi passi:
1. Sincronizza tutti gli articoli:
   npm run sync-blog
   
2. Oppure sincronizza un articolo specifico:
   npm run sync-blog-single 123
   
3. Lista articoli disponibili:
   npm run list-articles

4. Per maggiori informazioni:
   cat scripts/README.md

Buon blogging! 🚀
`);

    } catch (error) {
      console.error('💥 Errore durante il setup:', error.message);
    } finally {
      await this.close();
    }
  }
}

// Esegui il wizard
const wizard = new SetupWizard();
wizard.run().catch(console.error);
