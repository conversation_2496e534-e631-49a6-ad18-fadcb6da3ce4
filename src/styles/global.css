/*
  Unified CSS for <PERSON> website
  Integrates the theme system from biagiotti.me with Astro blog styling
 */

/* Import Bootstrap and Bootstrap Icons */
@import url('/bootstrap.min.css');
@import url('/bootstrap-icons.css');

/* Font & Color Variables */
:root {
  /* Fonts */
  --default-font: "Roboto", system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --heading-font: "Raleway", sans-serif;
  --nav-font: "Montserrat", sans-serif;

  /* Logo Colors */
  --logo-blue: #0071bc;
  --logo-light-blue: #29abe2;
  --logo-teal: #00a99d;
  --logo-green: #39b54a;
  --logo-light-green: #8cc63f;
  --logo-yellow: #f0c424;
  --logo-orange: #f85d23;
  --logo-red: #e5457a;
  --logo-pink: #e5457a;
  --logo-purple: #5c3896;

  /* Random Theme Control - Set to "yes" to enable random color selection on page load */
  --random-theme: no;

  /* Active Theme Color - Change this variable to use a different color from the logo */
  --theme-color: var(--logo-teal);

  /* Global Colors */
  --background-color: #ffffff;
  --default-color: #52413a;
  --heading-color: #31221c;
  --accent-color: var(--theme-color);
  --surface-color: #ffffff;
  --contrast-color: #ffffff;

  /* RGB version of theme color for opacity/rgba usage */
  --theme-color-rgb: 0, 169, 157;

  /* Nav Menu Colors */
  --nav-color: #52413a;
  --nav-hover-color: var(--theme-color);
  --nav-mobile-background-color: #ffffff;
  --nav-dropdown-background-color: #ffffff;
  --nav-dropdown-color: #52413a;
  --nav-dropdown-hover-color: var(--theme-color);

  /* Legacy blog variables for compatibility */
  --black: 15, 18, 25;
  --gray: 96, 115, 159;
  --gray-light: 229, 233, 240;
  --gray-dark: 34, 41, 57;
  --gray-gradient: rgba(var(--gray-light), 50%), #fff;
  --box-shadow: 0 2px 6px rgba(var(--gray), 25%), 0 8px 24px rgba(var(--gray), 33%),
    0 16px 32px rgba(var(--gray), 33%);
}
@font-face {
	font-family: 'Atkinson';
	src: url('/fonts/atkinson-regular.woff') format('woff');
	font-weight: 400;
	font-style: normal;
	font-display: swap;
}
@font-face {
	font-family: 'Atkinson';
	src: url('/fonts/atkinson-bold.woff') format('woff');
	font-weight: 700;
	font-style: normal;
	font-display: swap;
}
/* Color Presets */
.light-background {
  --background-color: #f8f5f4;
  --surface-color: #ffffff;
}

.dark-background {
  --background-color: #060606;
  --default-color: #ffffff;
  --heading-color: #ffffff;
  --surface-color: #343333;
  --contrast-color: #ffffff;
}

/* Smooth scroll */
:root {
  scroll-behavior: smooth;
}

/* General Styling */
body {
  color: var(--default-color);
  background-color: var(--background-color);
  font-family: var(--default-font);
  margin: 0;
  padding: 0;
  text-align: left;
  word-wrap: break-word;
  overflow-wrap: break-word;
  font-size: 16px;
  line-height: 1.6;
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: 0.3s;
}

a:hover {
  color: color-mix(in srgb, var(--accent-color), transparent 25%);
  text-decoration: none;
}

h1, h2, h3, h4, h5, h6 {
  color: var(--heading-color);
  font-family: var(--heading-font);
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}
main {
	width: 720px;
	max-width: calc(100% - 2em);
	margin: auto;
	padding: 3em 1em;
}
h1,
h2,
h3,
h4,
h5,
h6 {
	margin: 0 0 0.5rem 0;
	color: rgb(var(--black));
	line-height: 1.2;
}
h1 {
	font-size: 3.052em;
}
h2 {
	font-size: 2.441em;
}
h3 {
	font-size: 1.953em;
}
h4 {
	font-size: 1.563em;
}
h5 {
	font-size: 1.25em;
}
strong,
b {
	font-weight: 700;
}
a {
	color: var(--accent);
}
a:hover {
	color: var(--accent);
}
p {
	margin-bottom: 1em;
}
.prose p {
	margin-bottom: 2em;
}
textarea {
	width: 100%;
	font-size: 16px;
}
input {
	font-size: 16px;
}
table {
	width: 100%;
}
img {
	max-width: 100%;
	height: auto;
	border-radius: 8px;
}

/* Remove border-radius from logos */
.logo img,
.mobile-logo img {
	border-radius: 0;
}
code {
	padding: 2px 5px;
	background-color: rgb(var(--gray-light));
	border-radius: 2px;
}
pre {
	padding: 1.5em;
	border-radius: 8px;
}
pre > code {
	all: unset;
}
blockquote {
	border-left: 4px solid var(--accent);
	padding: 0 0 0 20px;
	margin: 0px;
	font-size: 1.333em;
}
hr {
	border: none;
	border-top: 1px solid rgb(var(--gray-light));
}
@media (max-width: 720px) {
	body {
		font-size: 18px;
	}
	main {
		padding: 1em;
	}
}

/* Header Styles */
.header {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 15px 0;
  transition: all 0.5s;
  z-index: 997;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header .logo {
  line-height: 1;
}

.header .logo img {
  max-height: 36px;
  margin-right: 8px;
  border-radius: 0 !important;
}

.header .btn-getstarted,
.header .btn-getstarted:focus {
  color: var(--contrast-color);
  background: var(--accent-color);
  font-size: 14px;
  padding: 8px 25px;
  margin: 0 0 0 30px;
  border-radius: 50px;
  transition: 0.3s;
  text-decoration: none;
}

.header .btn-getstarted:hover,
.header .btn-getstarted:focus:hover {
  color: var(--contrast-color);
  background: color-mix(in srgb, var(--accent-color), transparent 15%);
}

/* Navigation Menu Styles */
@media (min-width: 1200px) {
  .navmenu {
    padding: 0;
  }

  .navmenu ul {
    margin: 0;
    padding: 0;
    display: flex;
    list-style: none;
    align-items: center;
  }

  .navmenu li {
    position: relative;
  }

  .navmenu > ul > li {
    white-space: nowrap;
    padding: 15px 14px;
  }

  .navmenu > ul > li:last-child {
    padding-right: 0;
  }

  .navmenu a,
  .navmenu a:focus {
    color: var(--nav-color);
    font-size: 15px;
    padding: 0 2px;
    font-family: var(--nav-font);
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    transition: 0.3s;
    position: relative;
    text-decoration: none;
  }

  .navmenu > ul > li > a:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 2px;
    bottom: -6px;
    left: 0;
    background-color: var(--accent-color);
    visibility: hidden;
    width: 0px;
    transition: all 0.3s ease-in-out 0s;
  }

  .navmenu a:hover:before,
  .navmenu li:hover > a:before,
  .navmenu .active:before {
    visibility: visible;
    width: 100%;
  }

  .navmenu li:hover > a,
  .navmenu .active,
  .navmenu .active:focus {
    color: var(--nav-hover-color);
  }
}

/* Dropdown Menu Styles */
.navmenu ul li {
  position: relative;
}

.navmenu .dropdown-menu {
  display: none;
  position: absolute;
  left: 0;
  top: 100%;
  background-color: white;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  padding: 10px 0;
  min-width: 200px;
  z-index: 99;
}

.navmenu .dropdown-menu li {
  padding: 0;
  margin: 0;
}

.navmenu .dropdown-menu a {
  padding: 8px 20px;
  font-size: 14px;
  font-weight: 500;
  text-transform: none;
  color: #333;
  display: block;
  white-space: nowrap;
  transition: 0.3s;
}

.navmenu .dropdown-menu a:hover {
  color: var(--theme-color);
  background-color: #f8f9fa;
}

.navmenu li:hover > .dropdown-menu {
  display: block;
}

.navmenu .has-dropdown > a:after {
  content: "\f107";
  font-family: "Bootstrap-icons";
  margin-left: 5px;
  font-size: 14px;
}

/* Theme-specific styles */
.badge-theme {
  background-color: var(--theme-color);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.875rem;
}

.btn-theme {
  background-color: var(--theme-color);
  color: white !important;
  border: none;
  padding: 8px 20px;
  border-radius: 5px;
  text-decoration: none;
  transition: 0.3s;
  display: inline-block;
}

.btn-theme:hover {
  background-color: color-mix(in srgb, var(--theme-color), black 15%);
  opacity: 1;
  color: white !important;
  text-decoration: none;
}

.btn-outline-theme {
  border: 1px solid var(--theme-color);
  color: var(--theme-color) !important;
  background: transparent;
  padding: 8px 20px;
  border-radius: 5px;
  text-decoration: none;
  transition: 0.3s;
  display: inline-block;
}

.btn-outline-theme:hover {
  background-color: var(--theme-color);
  color: white !important;
  text-decoration: none;
}

/* Fix for all button hover states */
.btn:hover {
  text-decoration: none;
}

/* Ensure badge styling is consistent */
.badge-theme {
  background-color: var(--theme-color) !important;
  color: white !important;
}

/* Fix navigation hover states */
.navmenu a:hover {
  text-decoration: none;
}

/* Mobile Navigation */
@media (max-width: 1199px) {
  .navmenu .dropdown-menu {
    position: static;
    display: none;
    width: 100%;
    box-shadow: none;
    background-color: #f8f9fa;
    padding: 0;
    margin-top: 10px;
    border-radius: 0;
  }

  .navmenu .dropdown-menu.show {
    display: block;
  }

  .navmenu .dropdown-menu a {
    padding: 10px 20px 10px 35px;
  }

  .navmenu li:hover > .dropdown-menu {
    display: none;
  }

  .navmenu .has-dropdown > a:after {
    float: right;
  }

  .mobile-nav-active .navmenu {
    position: fixed;
    overflow: hidden;
    inset: 0;
    background: rgba(33, 37, 41, 0.8);
    transition: 0.3s;
  }

  .mobile-nav-active .navmenu ul {
    display: block;
    position: absolute;
    inset: 60px 20px 20px 20px;
    padding: 10px 0;
    margin: 0;
    border-radius: 6px;
    background-color: var(--nav-mobile-background-color);
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: none;
    overflow-y: auto;
    transition: 0.3s;
    z-index: 9998;
  }
}

.sr-only {
  border: 0;
  padding: 0;
  margin: 0;
  position: absolute !important;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(1px 1px 1px 1px);
  clip: rect(1px, 1px, 1px, 1px);
  clip-path: inset(50%);
  white-space: nowrap;
}
