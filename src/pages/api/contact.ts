import type { APIRoute } from 'astro';

export const prerender = false;

export const POST: APIRoute = async ({ request }) => {
  try {
    const body = await request.json();
    const { name, email, subject, message } = body;

    // Validate required fields
    if (!name || !email || !message) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Nome, email e messaggio sono obbligatori'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // Validate email format
    if (!email.includes('@')) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Indirizzo email non valido'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // For now, just log the contact submission
    // In production, you would save to database and send email
    console.log('Contact submission received:', {
      name: name.trim(),
      email: email.trim(),
      subject: subject?.trim(),
      message: message.trim(),
      timestamp: new Date().toISOString()
    });

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 500));

    return new Response(JSON.stringify({
      success: true,
      message: 'Messaggio inviato con successo! Ti risponderò presto.'
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });

  } catch (error) {
    console.error('Contact API error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: 'Errore interno del server'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
