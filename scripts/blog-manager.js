#!/usr/bin/env node

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import readline from 'readline';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const BLOG_CONTENT_DIR = path.join(__dirname, '..', 'src', 'content', 'blog');

class BlogManager {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  async question(prompt) {
    return new Promise((resolve) => {
      this.rl.question(prompt, resolve);
    });
  }

  async close() {
    this.rl.close();
  }

  /**
   * Legge il frontmatter di un file Markdown
   */
  async readFrontmatter(filePath) {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const frontmatterMatch = content.match(/^---\n([\s\S]*?)\n---/);
      
      if (!frontmatterMatch) {
        throw new Error('Frontmatter non trovato');
      }
      
      const frontmatterText = frontmatterMatch[1];
      const bodyContent = content.replace(/^---\n[\s\S]*?\n---\n/, '');
      
      // Parse YAML semplificato
      const frontmatter = this.parseSimpleYAML(frontmatterText);
      
      return { frontmatter, content: bodyContent, fullContent: content };
    } catch (error) {
      console.error(`Errore nella lettura di ${filePath}:`, error.message);
      return null;
    }
  }

  /**
   * Parser YAML semplificato per il frontmatter
   */
  parseSimpleYAML(yamlText) {
    const result = {};
    const lines = yamlText.split('\n');
    let currentKey = null;
    let currentValue = '';
    let inMultiline = false;
    let inObject = false;
    let objectContent = '';

    for (let line of lines) {
      line = line.trim();
      
      if (!line || line.startsWith('#')) continue;
      
      // Gestione oggetti JSON
      if (line.includes(': {') || line.includes(':{')) {
        inObject = true;
        const [key] = line.split(':');
        currentKey = key.trim();
        objectContent = line.substring(line.indexOf('{'));
        continue;
      }
      
      if (inObject) {
        objectContent += ' ' + line;
        if (line.includes('}')) {
          try {
            result[currentKey] = JSON.parse(objectContent);
          } catch {
            result[currentKey] = objectContent;
          }
          inObject = false;
          objectContent = '';
          currentKey = null;
        }
        continue;
      }
      
      // Gestione valori multilinea
      if (line.includes(': |') || line.includes(':|')) {
        inMultiline = true;
        currentKey = line.split(':')[0].trim();
        currentValue = '';
        continue;
      }
      
      if (inMultiline) {
        if (line.startsWith('  ')) {
          currentValue += (currentValue ? '\n' : '') + line.substring(2);
        } else {
          result[currentKey] = currentValue;
          inMultiline = false;
          currentKey = null;
          currentValue = '';
          // Processa la linea corrente come normale
        }
      }
      
      if (!inMultiline && !inObject && line.includes(':')) {
        const [key, ...valueParts] = line.split(':');
        let value = valueParts.join(':').trim();
        
        // Rimuovi quotes
        if ((value.startsWith("'") && value.endsWith("'")) || 
            (value.startsWith('"') && value.endsWith('"'))) {
          value = value.slice(1, -1);
        }
        
        // Converti tipi
        if (value === 'true') value = true;
        else if (value === 'false') value = false;
        else if (value.match(/^\d+$/)) value = parseInt(value);
        else if (value.startsWith('[') && value.endsWith(']')) {
          try {
            value = JSON.parse(value);
          } catch {
            // Mantieni come stringa se non è JSON valido
          }
        }
        
        result[key.trim()] = value;
      }
    }
    
    // Gestisci ultimo valore multilinea
    if (inMultiline && currentKey) {
      result[currentKey] = currentValue;
    }
    
    return result;
  }

  /**
   * Scrive il frontmatter aggiornato
   */
  async updateFrontmatter(filePath, newFrontmatter, content) {
    try {
      const yamlContent = this.generateYAML(newFrontmatter);
      const fullContent = `---\n${yamlContent}\n---\n\n${content}`;
      
      await fs.writeFile(filePath, fullContent, 'utf8');
      return true;
    } catch (error) {
      console.error(`Errore nella scrittura di ${filePath}:`, error.message);
      return false;
    }
  }

  /**
   * Genera YAML dal frontmatter
   */
  generateYAML(obj) {
    return Object.entries(obj)
      .map(([key, value]) => {
        if (value === null || value === undefined) return null;
        
        if (Array.isArray(value)) {
          return `${key}: [${value.map(v => `'${v}'`).join(', ')}]`;
        }
        
        if (typeof value === 'object' && value !== null) {
          return `${key}: ${JSON.stringify(value)}`;
        }
        
        if (typeof value === 'string' && value.includes('\n')) {
          return `${key}: |\n  ${value.split('\n').join('\n  ')}`;
        }
        
        if (typeof value === 'boolean') {
          return `${key}: ${value}`;
        }
        
        return `${key}: '${value}'`;
      })
      .filter(Boolean)
      .join('\n');
  }

  /**
   * Ottieni tutti i file del blog
   */
  async getBlogFiles() {
    try {
      const files = await fs.readdir(BLOG_CONTENT_DIR);
      return files.filter(file => file.endsWith('.md') || file.endsWith('.mdx'));
    } catch (error) {
      console.error('Errore nella lettura della directory blog:', error.message);
      return [];
    }
  }

  /**
   * Lista tutti gli articoli con il loro stato
   */
  async listArticles() {
    const files = await this.getBlogFiles();
    const articles = [];

    for (const file of files) {
      const filePath = path.join(BLOG_CONTENT_DIR, file);
      const data = await this.readFrontmatter(filePath);
      
      if (data) {
        articles.push({
          filename: file,
          title: data.frontmatter.title || 'Senza titolo',
          draft: data.frontmatter.draft || false,
          published: data.frontmatter.published !== false,
          pubDate: data.frontmatter.pubDate,
          featured: data.frontmatter.featured || false,
          scheduledDate: data.frontmatter.scheduledDate,
          autoPublish: data.frontmatter.autoPublish || false
        });
      }
    }

    return articles.sort((a, b) => new Date(b.pubDate) - new Date(a.pubDate));
  }

  /**
   * Mostra la lista degli articoli
   */
  async showArticlesList() {
    const articles = await this.listArticles();
    
    if (articles.length === 0) {
      console.log('📭 Nessun articolo trovato');
      return;
    }

    console.log('\n📚 Articoli del Blog:\n');
    console.log('ID  | Stato        | Titolo                                    | Data       | Programmazione');
    console.log('----|--------------|-------------------------------------------|------------|---------------');

    articles.forEach((article, index) => {
      const id = (index + 1).toString().padStart(2, ' ');

      let status;
      let scheduledInfo = '';

      if (article.draft) {
        status = '📝 DRAFT';
      } else if (article.autoPublish && article.scheduledDate) {
        const scheduleTime = new Date(article.scheduledDate);
        const now = new Date();

        if (scheduleTime > now) {
          status = '⏰ SCHEDULED';
          scheduledInfo = new Date(article.scheduledDate).toLocaleDateString('it-IT') + ' ' +
                         new Date(article.scheduledDate).toLocaleTimeString('it-IT', { hour: '2-digit', minute: '2-digit' });
        } else {
          status = '🚀 READY';
          scheduledInfo = 'Pronto per pubblicazione';
        }
      } else if (article.published) {
        status = '✅ LIVE';
      } else {
        status = '⏸️  PAUSED';
      }

      const title = article.title.length > 40 ?
                   article.title.substring(0, 37) + '...' :
                   article.title.padEnd(40);
      const date = new Date(article.pubDate).toLocaleDateString('it-IT');

      console.log(`${id}  | ${status.padEnd(12)} | ${title} | ${date} | ${scheduledInfo}`);
    });
    
    console.log('\nLegenda:');
    console.log('📝 DRAFT     = Bozza (non visibile sul sito)');
    console.log('✅ LIVE      = Pubblicato (visibile sul sito)');
    console.log('⏸️  PAUSED    = In pausa (importato ma non pubblicato)');
    console.log('⏰ SCHEDULED = Programmato per pubblicazione futura');
    console.log('🚀 READY     = Pronto per pubblicazione automatica');
  }

  /**
   * Cambia lo stato di pubblicazione di un articolo
   */
  async changeArticleStatus(articleIndex, newStatus) {
    const articles = await this.listArticles();
    
    if (articleIndex < 1 || articleIndex > articles.length) {
      console.log('❌ Numero articolo non valido');
      return false;
    }

    const article = articles[articleIndex - 1];
    const filePath = path.join(BLOG_CONTENT_DIR, article.filename);
    const data = await this.readFrontmatter(filePath);
    
    if (!data) {
      console.log('❌ Errore nella lettura dell\'articolo');
      return false;
    }

    // Aggiorna lo stato
    const updatedFrontmatter = { ...data.frontmatter };
    
    switch (newStatus) {
      case 'draft':
        updatedFrontmatter.draft = true;
        updatedFrontmatter.published = false;
        updatedFrontmatter.autoPublish = false;
        delete updatedFrontmatter.scheduledDate;
        break;
      case 'publish':
        updatedFrontmatter.draft = false;
        updatedFrontmatter.published = true;
        updatedFrontmatter.autoPublish = false;
        updatedFrontmatter.publishDate = new Date().toISOString().split('T')[0];
        delete updatedFrontmatter.scheduledDate;
        break;
      case 'pause':
        updatedFrontmatter.draft = false;
        updatedFrontmatter.published = false;
        updatedFrontmatter.autoPublish = false;
        delete updatedFrontmatter.scheduledDate;
        break;
    }

    const success = await this.updateFrontmatter(filePath, updatedFrontmatter, data.content);
    
    if (success) {
      const statusText = newStatus === 'draft' ? 'DRAFT' : 
                        newStatus === 'publish' ? 'PUBBLICATO' : 'IN PAUSA';
      console.log(`✅ Articolo "${article.title}" impostato come ${statusText}`);
      return true;
    }
    
    return false;
  }

  /**
   * Programma la pubblicazione di un articolo
   */
  async scheduleArticle(articleIndex, scheduledDate) {
    const articles = await this.listArticles();

    if (articleIndex < 1 || articleIndex > articles.length) {
      console.log('❌ Numero articolo non valido');
      return false;
    }

    const article = articles[articleIndex - 1];
    const filePath = path.join(BLOG_CONTENT_DIR, article.filename);
    const data = await this.readFrontmatter(filePath);

    if (!data) {
      console.log('❌ Errore nella lettura dell\'articolo');
      return false;
    }

    // Valida la data
    const scheduleTime = new Date(scheduledDate);
    const now = new Date();

    if (scheduleTime <= now) {
      console.log('❌ La data deve essere nel futuro');
      return false;
    }

    // Aggiorna il frontmatter
    const updatedFrontmatter = { ...data.frontmatter };
    updatedFrontmatter.draft = false;
    updatedFrontmatter.published = false;
    updatedFrontmatter.autoPublish = true;
    updatedFrontmatter.scheduledDate = scheduleTime.toISOString();

    const success = await this.updateFrontmatter(filePath, updatedFrontmatter, data.content);

    if (success) {
      console.log(`⏰ Articolo "${article.title}" programmato per ${scheduleTime.toLocaleDateString('it-IT')} alle ${scheduleTime.toLocaleTimeString('it-IT', { hour: '2-digit', minute: '2-digit' })}`);
      return true;
    }

    return false;
  }

  /**
   * Pubblica automaticamente gli articoli pronti
   */
  async publishScheduledPosts() {
    const articles = await this.listArticles();
    const now = new Date();

    const readyPosts = articles.filter(article => {
      if (!article.autoPublish || !article.scheduledDate || article.published) {
        return false;
      }

      const scheduleTime = new Date(article.scheduledDate);
      return scheduleTime <= now;
    });

    if (readyPosts.length === 0) {
      console.log('📭 Nessun articolo pronto per la pubblicazione automatica');
      return { published: 0, total: 0 };
    }

    console.log(`🚀 Trovati ${readyPosts.length} articoli pronti per la pubblicazione:`);

    let published = 0;
    for (const post of readyPosts) {
      try {
        const articleIndex = articles.findIndex(a => a.filename === post.filename) + 1;
        const success = await this.changeArticleStatus(articleIndex, 'publish');
        if (success) {
          published++;
          console.log(`✅ Pubblicato: ${post.title}`);
        }
      } catch (error) {
        console.error(`❌ Errore nella pubblicazione di "${post.title}":`, error.message);
      }
    }

    console.log(`\n📊 Pubblicazione automatica completata: ${published}/${readyPosts.length} articoli`);
    return { published, total: readyPosts.length };
  }

  /**
   * Gestione interattiva
   */
  async interactiveMode() {
    console.log('\n🎛️  Modalità Interattiva - Gestione Blog\n');
    
    while (true) {
      await this.showArticlesList();
      
      console.log('\nComandi disponibili:');
      console.log('  [numero] publish           - Pubblica articolo');
      console.log('  [numero] draft             - Imposta come bozza');
      console.log('  [numero] pause             - Metti in pausa');
      console.log('  [numero] schedule [data]   - Programma pubblicazione (YYYY-MM-DD HH:MM)');
      console.log('  auto-publish               - Pubblica articoli programmati pronti');
      console.log('  refresh                    - Aggiorna lista');
      console.log('  quit                       - Esci');
      
      const input = await this.question('\n> ');
      const parts = input.trim().split(' ');
      const [command, action, ...dateParts] = parts;

      if (command === 'quit' || command === 'q') {
        break;
      }

      if (command === 'refresh' || command === 'r') {
        continue;
      }

      if (command === 'auto-publish') {
        await this.publishScheduledPosts();
        console.log('\nPremi ENTER per continuare...');
        await this.question('');
        continue;
      }

      const articleNum = parseInt(command);
      if (articleNum && action) {
        if (['publish', 'draft', 'pause'].includes(action)) {
          await this.changeArticleStatus(articleNum, action);
          console.log('\nPremi ENTER per continuare...');
          await this.question('');
        } else if (action === 'schedule') {
          if (dateParts.length === 0) {
            console.log('❌ Specifica la data: [numero] schedule YYYY-MM-DD HH:MM');
          } else {
            const dateString = dateParts.join(' ');
            try {
              // Prova diversi formati di data
              let scheduledDate;
              if (dateString.includes(' ')) {
                // Formato con ora: YYYY-MM-DD HH:MM
                scheduledDate = new Date(dateString);
              } else {
                // Solo data: YYYY-MM-DD (imposta alle 09:00)
                scheduledDate = new Date(dateString + ' 09:00');
              }

              if (isNaN(scheduledDate.getTime())) {
                throw new Error('Data non valida');
              }

              await this.scheduleArticle(articleNum, scheduledDate);
              console.log('\nPremi ENTER per continuare...');
              await this.question('');
            } catch (error) {
              console.log('❌ Formato data non valido. Usa: YYYY-MM-DD o YYYY-MM-DD HH:MM');
            }
          }
        } else {
          console.log('❌ Azione non valida. Usa: publish, draft, pause, schedule');
        }
      } else {
        console.log('❌ Comando non valido. Esempi:');
        console.log('  1 publish');
        console.log('  2 schedule 2024-02-15 10:30');
        console.log('  3 draft');
        console.log('  auto-publish');
      }
    }
  }

  /**
   * Pubblica tutti i draft
   */
  async publishAllDrafts() {
    const articles = await this.listArticles();
    const drafts = articles.filter(a => a.draft);
    
    if (drafts.length === 0) {
      console.log('📭 Nessuna bozza da pubblicare');
      return;
    }

    console.log(`📝 Trovate ${drafts.length} bozze da pubblicare:`);
    drafts.forEach((draft, index) => {
      console.log(`  ${index + 1}. ${draft.title}`);
    });

    const confirm = await this.question('\nVuoi pubblicare tutte le bozze? (y/N): ');
    if (confirm.toLowerCase() !== 'y') {
      console.log('❌ Operazione annullata');
      return;
    }

    let published = 0;
    for (const draft of drafts) {
      const articleIndex = articles.findIndex(a => a.filename === draft.filename) + 1;
      const success = await this.changeArticleStatus(articleIndex, 'publish');
      if (success) published++;
    }

    console.log(`✅ Pubblicati ${published}/${drafts.length} articoli`);
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'help';
  
  const manager = new BlogManager();
  
  try {
    switch (command) {
      case 'list':
        await manager.showArticlesList();
        break;
        
      case 'interactive':
      case 'i':
        await manager.interactiveMode();
        break;
        
      case 'publish':
        if (args[1] === 'all') {
          await manager.publishAllDrafts();
        } else if (args[1]) {
          const articleNum = parseInt(args[1]);
          await manager.changeArticleStatus(articleNum, 'publish');
        } else {
          console.log('❌ Specifica il numero dell\'articolo o "all"');
        }
        break;
        
      case 'draft':
        if (args[1]) {
          const articleNum = parseInt(args[1]);
          await manager.changeArticleStatus(articleNum, 'draft');
        } else {
          console.log('❌ Specifica il numero dell\'articolo');
        }
        break;
        
      case 'pause':
        if (args[1]) {
          const articleNum = parseInt(args[1]);
          await manager.changeArticleStatus(articleNum, 'pause');
        } else {
          console.log('❌ Specifica il numero dell\'articolo');
        }
        break;

      case 'schedule':
        if (args[1] && args[2]) {
          const articleNum = parseInt(args[1]);
          const dateString = args.slice(2).join(' ');

          try {
            let scheduledDate;
            if (dateString.includes(' ')) {
              scheduledDate = new Date(dateString);
            } else {
              scheduledDate = new Date(dateString + ' 09:00');
            }

            if (isNaN(scheduledDate.getTime())) {
              throw new Error('Data non valida');
            }

            await manager.scheduleArticle(articleNum, scheduledDate);
          } catch (error) {
            console.log('❌ Formato data non valido. Usa: YYYY-MM-DD o YYYY-MM-DD HH:MM');
          }
        } else {
          console.log('❌ Specifica numero articolo e data: schedule [numero] [YYYY-MM-DD HH:MM]');
        }
        break;

      case 'auto-publish':
        await manager.publishScheduledPosts();
        break;
        
      case 'help':
      default:
        console.log(`
🎛️  Blog Manager - Gestione Pubblicazione Articoli

Comandi disponibili:
  list                              Mostra tutti gli articoli con stato
  interactive                       Modalità interattiva
  publish [numero|all]              Pubblica articolo specifico o tutte le bozze
  draft [numero]                    Imposta articolo come bozza
  pause [numero]                    Metti articolo in pausa
  schedule [numero] [data]          Programma pubblicazione futura
  auto-publish                      Pubblica articoli programmati pronti

Esempi:
  node scripts/blog-manager.js list
  node scripts/blog-manager.js interactive
  node scripts/blog-manager.js publish 3
  node scripts/blog-manager.js publish all
  node scripts/blog-manager.js draft 2
  node scripts/blog-manager.js schedule 1 2024-02-15
  node scripts/blog-manager.js schedule 1 "2024-02-15 10:30"
  node scripts/blog-manager.js auto-publish

Stati degli articoli:
  📝 DRAFT     = Bozza (non visibile sul sito)
  ✅ LIVE      = Pubblicato (visibile sul sito)
  ⏸️  PAUSED    = In pausa (importato ma non pubblicato)
  ⏰ SCHEDULED = Programmato per pubblicazione futura
  🚀 READY     = Pronto per pubblicazione automatica

Programmazione:
  - Usa formato data: YYYY-MM-DD o "YYYY-MM-DD HH:MM"
  - Gli articoli programmati diventano visibili automaticamente alla data impostata
  - Esegui 'auto-publish' per pubblicare manualmente gli articoli pronti

Nota: Gli articoli importati da Strapi sono impostati come DRAFT per default.
      Usa questo tool per decidere quali e quando pubblicare.
        `);
        break;
    }
  } finally {
    await manager.close();
  }
}

// Esegui solo se chiamato direttamente
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export default BlogManager;
