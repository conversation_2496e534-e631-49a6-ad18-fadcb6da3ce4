---
// Push notifications component for new blog posts
---

<div id="notification-banner" class="notification-banner" style="display: none;">
  <div class="notification-content">
    <span class="notification-icon">🔔</span>
    <div class="notification-text">
      <strong>Resta aggiornato!</strong>
      <p>Ricevi notifiche per i nuovi articoli</p>
    </div>
    <div class="notification-actions">
      <button id="enable-notifications" class="btn-enable">Attiva</button>
      <button id="dismiss-notifications" class="btn-dismiss">✕</button>
    </div>
  </div>
</div>

<script>
  class PushNotificationManager {
    constructor() {
      this.vapidPublicKey = 'YOUR_VAPID_PUBLIC_KEY'; // Replace with your VAPID key
      this.init();
    }

    async init() {
      // Check if service worker and push notifications are supported
      if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
        console.log('Push notifications not supported');
        return;
      }

      // Check if user has already made a decision
      const permission = await this.getNotificationPermission();
      if (permission === 'default') {
        this.showNotificationBanner();
      } else if (permission === 'granted') {
        this.subscribeUser();
      }

      this.bindEvents();
    }

    bindEvents() {
      const enableBtn = document.getElementById('enable-notifications');
      const dismissBtn = document.getElementById('dismiss-notifications');

      enableBtn?.addEventListener('click', () => this.requestPermission());
      dismissBtn?.addEventListener('click', () => this.dismissBanner());
    }

    showNotificationBanner() {
      const banner = document.getElementById('notification-banner');
      if (banner) {
        banner.style.display = 'block';
        
        // Auto-hide after 10 seconds
        setTimeout(() => {
          this.dismissBanner();
        }, 10000);
      }
    }

    dismissBanner() {
      const banner = document.getElementById('notification-banner');
      if (banner) {
        banner.style.display = 'none';
        localStorage.setItem('notifications-dismissed', 'true');
      }
    }

    async getNotificationPermission() {
      if ('Notification' in window) {
        return Notification.permission;
      }
      return 'denied';
    }

    async requestPermission() {
      try {
        const permission = await Notification.requestPermission();
        
        if (permission === 'granted') {
          this.dismissBanner();
          await this.subscribeUser();
          this.showSuccessMessage();
        } else {
          this.showErrorMessage('Permesso negato per le notifiche');
        }
      } catch (error) {
        console.error('Error requesting notification permission:', error);
        this.showErrorMessage('Errore durante la richiesta di permesso');
      }
    }

    async subscribeUser() {
      try {
        const registration = await navigator.serviceWorker.ready;
        
        const subscription = await registration.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: this.urlBase64ToUint8Array(this.vapidPublicKey)
        });

        // Send subscription to server
        await this.sendSubscriptionToServer(subscription);
        
      } catch (error) {
        console.error('Error subscribing user:', error);
      }
    }

    async sendSubscriptionToServer(subscription) {
      try {
        await fetch('/api/notifications/subscribe', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            subscription: subscription,
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString()
          })
        });
      } catch (error) {
        console.error('Error sending subscription to server:', error);
      }
    }

    urlBase64ToUint8Array(base64String) {
      const padding = '='.repeat((4 - base64String.length % 4) % 4);
      const base64 = (base64String + padding)
        .replace(/-/g, '+')
        .replace(/_/g, '/');

      const rawData = window.atob(base64);
      const outputArray = new Uint8Array(rawData.length);

      for (let i = 0; i < rawData.length; ++i) {
        outputArray[i] = rawData.charCodeAt(i);
      }
      return outputArray;
    }

    showSuccessMessage() {
      this.showToast('✅ Notifiche attivate con successo!', 'success');
    }

    showErrorMessage(message) {
      this.showToast(`❌ ${message}`, 'error');
    }

    showToast(message, type) {
      const toast = document.createElement('div');
      toast.className = `toast toast-${type}`;
      toast.textContent = message;
      
      document.body.appendChild(toast);
      
      // Show toast
      setTimeout(() => toast.classList.add('show'), 100);
      
      // Hide toast after 3 seconds
      setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => document.body.removeChild(toast), 300);
      }, 3000);
    }
  }

  // Initialize push notifications
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => new PushNotificationManager());
  } else {
    new PushNotificationManager();
  }
</script>

<style>
  .notification-banner {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    animation: slideDown 0.3s ease-out;
  }

  .notification-content {
    display: flex;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
    gap: 1rem;
  }

  .notification-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
  }

  .notification-text {
    flex: 1;
  }

  .notification-text strong {
    display: block;
    margin-bottom: 0.25rem;
  }

  .notification-text p {
    margin: 0;
    font-size: 0.875rem;
    opacity: 0.9;
  }

  .notification-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }

  .btn-enable {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
  }

  .btn-enable:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  .btn-dismiss {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.25rem;
    opacity: 0.7;
    transition: opacity 0.2s ease;
  }

  .btn-dismiss:hover {
    opacity: 1;
  }

  .toast {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    transform: translateY(100px);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 1001;
  }

  .toast.show {
    transform: translateY(0);
    opacity: 1;
  }

  .toast-success {
    background: #10b981;
  }

  .toast-error {
    background: #ef4444;
  }

  @keyframes slideDown {
    from {
      transform: translateY(-100%);
    }
    to {
      transform: translateY(0);
    }
  }

  @media (max-width: 768px) {
    .notification-content {
      padding: 1rem;
      flex-direction: column;
      text-align: center;
      gap: 0.75rem;
    }

    .notification-actions {
      width: 100%;
      justify-content: center;
    }

    .toast {
      bottom: 1rem;
      right: 1rem;
      left: 1rem;
    }
  }
</style>
