import type { APIRoute } from 'astro';

export const prerender = false;

export const POST: APIRoute = async ({ request }) => {
  try {
    const body = await request.json();
    const { email, name } = body;

    // Validate email
    if (!email || !email.includes('@')) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Indirizzo email non valido'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // For now, just log the newsletter subscription
    // In production, you would save to database and send welcome email
    console.log('Newsletter subscription received:', {
      email: email.trim(),
      name: name?.trim() || '',
      timestamp: new Date().toISOString()
    });

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 500));

    return new Response(JSON.stringify({
      success: true,
      message: 'Iscrizione completata! Controlla la tua email per la conferma.'
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });

  } catch (error) {
    console.error('Newsletter API error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: 'Errore interno del server'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
