// scripts/strapi-config.js

// Default configuration values for Strapi sync script
// Modify these values according to your Strapi setup and preferences.

const defaultConfig = {
  // Base URL of your Strapi instance (e.g., 'http://localhost:1337')
  baseUrl: process.env.STRAPI_BASE_URL || 'http://localhost:1337',

  // API endpoint for fetching articles
  articlesEndpoint: '/articles', // Example: '/posts' or '/blog-posts'

  // Query parameters for populating relations in Strapi
  // See Strapi documentation for available parameters:
  // https://docs.strapi.io/developer-docs/latest/developer-resources/database-apis-reference/rest/populating-fields.html
  // Example: 'populate=*,Immagine,seo,openGraph,categoria,tags'
  populateParams: 'populate=*',

  // Output directories and settings
  output: {
    // Directory where blog markdown files will be saved (relative to project root)
    blogDir: 'src/content/blog',
    defaultAuthor: 'Default Author',
    defaultCategory: 'General',
  },

  // Rich text and image handling
  richTextConfig: {
    imageConfig: {
      // Whether to download images from Strapi to the local project
      downloadImages: true,
      // Local directory to store downloaded images (relative to project root)
      localImageDir: 'public/uploads/strapi',
    },
  },

  // Filters for fetching articles
  filters: {
    // If true, only fetch articles that have a 'publishedAt' date
    publishedOnly: true,
  },

  // Backup settings
  backupExisting: true, // If true, creates a backup of existing files before overwriting
  backupDir: '.strapi-sync-backups', // Directory to store backups

  // General script behavior
  dryRun: false, // If true, simulates operations without writing files
  verbose: true, // If true, enables detailed logging
};

// Environment-specific configurations (optional)
const developmentConfig = {
  ...defaultConfig,
  baseUrl: process.env.STRAPI_DEV_BASE_URL || process.env.STRAPI_BASE_URL || 'http://localhost:1337',
  verbose: true,
  dryRun: false,
};

const productionConfig = {
  ...defaultConfig,
  baseUrl: process.env.STRAPI_PROD_BASE_URL || 'https://your-production-strapi-url.com',
  filters: {
    publishedOnly: true,
  },
  verbose: false,
  dryRun: false,
};

/**
 * Returns the configuration based on NODE_ENV
 * Defaults to development configuration.
 */
export function getConfig() {
  const env = process.env.NODE_ENV || 'development';
  if (env === 'production') {
    return productionConfig;
  }
  return developmentConfig;
}

// Example of how to use environment variables for sensitive data:
// STRAPI_API_TOKEN is typically set in your .env file or CI/CD environment
if (!process.env.STRAPI_API_TOKEN) {
  console.warn('Warning: STRAPI_API_TOKEN is not set. Authentication with Strapi might fail.');
}
