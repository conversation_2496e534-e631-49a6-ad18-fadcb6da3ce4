# 🐛 Troubleshooting - Blog <PERSON> per risolvere i problemi più comuni del blog.

## 🚨 Errori Risolti

### ✅ Errore JavaScript "Unexpected identifier 'as'"

**Problema**: Sintassi TypeScript in script client-side
```
Uncaught SyntaxError: Unexpected identifier 'as'
```

**Causa**: Type annotations TypeScript in codice che viene eseguito nel browser

**Soluzione**: Rimuovere le type annotations dai script client-side

```javascript
// ❌ Errato (causa errore)
function showMessage(message: string, type: 'success' | 'error') {
  // codice
}

// ✅ Corretto
function showMessage(message, type) {
  // codice
}
```

**File interessati**:
- `src/components/Newsletter.astro`
- `src/components/PostRating.astro`
- Altri componenti con script client-side

### ✅ Font Bootstrap Icons non caricati

**Problema**: Errori 404 per i font Bootstrap Icons
```
GET http://localhost:4321/blog/post/fonts/bootstrap-icons.woff2 404 (Not Found)
```

**Causa**: Percorsi relativi nei CSS che non funzionano nelle sottopagine

**Soluzione**: Usare percorsi assoluti nel CSS

```css
/* ❌ Errato (percorso relativo) */
@font-face {
  src: url("./fonts/bootstrap-icons.woff2") format("woff2");
}

/* ✅ Corretto (percorso assoluto) */
@font-face {
  src: url("/fonts/bootstrap-icons.woff2") format("woff2");
}
```

**File da modificare**: `public/bootstrap-icons.css`

### ✅ Menu mobile ridisegnato completamente

**Problema**: Su mobile il menu appare come lista puntata invece di hamburger menu professionale
```
Menu mobile non professionale, comportamento inconsistente
```

**Causa**: Implementazione menu mobile inadeguata per UX moderna

**Soluzione**: Creato menu mobile completamente nuovo con modale a tutta pagina

**Implementazione**:
```html
<!-- Header con separazione desktop/mobile -->
<nav class="navmenu d-none d-xl-flex">
  <!-- Menu desktop -->
</nav>

<button class="mobile-menu-toggle d-xl-none">
  <span class="hamburger-line"></span>
  <span class="hamburger-line"></span>
  <span class="hamburger-line"></span>
</button>

<!-- Modale mobile -->
<div class="mobile-menu-overlay">
  <div class="mobile-menu-container">
    <!-- Menu mobile con dropdown accordion -->
  </div>
</div>
```

**JavaScript**:
```javascript
// Apertura menu
mobileMenuToggle.addEventListener('click', function() {
  mobileMenuOverlay.classList.add('active');
  document.body.classList.add('mobile-menu-open');
  this.classList.add('active');
});

// Chiusura multipla
function closeMobileMenu() {
  mobileMenuOverlay.classList.remove('active');
  document.body.classList.remove('mobile-menu-open');
  mobileMenuToggle.classList.remove('active');
}
```

**Funzionalità implementate**:
- ✅ **Hamburger animato** (3 linee → X)
- ✅ **Modale a tutta pagina** con overlay
- ✅ **Slide-in da destra** con animazioni
- ✅ **Dropdown accordion** funzionanti
- ✅ **Chiusura multipla** (X, overlay, ESC, link)
- ✅ **Prevenzione scroll** body
- ✅ **Design responsive** e accessibile

### ✅ Errore Utterances commenti

**Problema**: Errore 422 da GitHub API per Utterances
```
GET https://api.github.com/search/issues?q="blog/post" 422 (Unprocessable Content)
```

**Causa**: Repository GitHub non configurato correttamente

**Soluzione**:
1. **Verifica repository**: Deve essere pubblico
2. **Installa Utterances app**: [utteranc.es](https://utteranc.es)
3. **Abilita Issues** nel repository
4. **Correggi nome repository** in `src/components/Comments.astro`:

```astro
const { 
  repo = 'mbiagiottime/biagiotti-blog', // Nome corretto
  theme = 'github-light',
  issueTerm = 'pathname',
  label = 'comment'
} = Astro.props;
```

### ✅ Tag e categorie non funzionanti

**Problema**: Link a tag/categorie danno 404
```
GET http://localhost:4321/tags/development/ 404 (Not Found)
```

**Causa**: Pagine dinamiche non create

**Soluzione**: Creare le pagine dinamiche
- `src/pages/categories/[category].astro`
- `src/pages/tags/[tag].astro`

**Implementazione**:
```astro
---
// src/pages/tags/[tag].astro
export async function getStaticPaths() {
  const tags = await getAllTags();
  return tags.map((tag) => ({
    params: { tag: tag.toLowerCase() },
    props: { tag },
  }));
}
---
```

## 🔧 Problemi Comuni

### Build Errors

#### 1. "Module not found"

**Errore**:
```
Error: Could not resolve "./components/Component"
```

**Soluzioni**:
```bash
# Verifica percorso file
ls src/components/Component.astro

# Controlla import
import Component from '../components/Component.astro';

# Pulisci cache
rm -rf node_modules/.astro
npm run dev
```

#### 2. "Type error in frontmatter"

**Errore**:
```
Error: Invalid frontmatter in blog post
```

**Soluzioni**:
```yaml
# Verifica formato date
pubDate: 2024-01-15  # ✅ Corretto
pubDate: "15/01/2024"  # ❌ Errato

# Verifica array tags
tags: ['tag1', 'tag2']  # ✅ Corretto
tags: tag1, tag2  # ❌ Errato
```

#### 3. "Image optimization failed"

**Errore**:
```
Error: Could not process image
```

**Soluzioni**:
```bash
# Verifica formato immagine
file public/image.jpg

# Riduci dimensioni se troppo grande
# Max raccomandato: 2MB per immagine

# Usa formati supportati: jpg, png, webp, avif
```

### Runtime Errors

#### 1. "Hydration mismatch"

**Errore**:
```
Warning: Text content did not match
```

**Soluzioni**:
```astro
<!-- Usa client:load per componenti dinamici -->
<Component client:load />

<!-- Evita contenuto dinamico nel SSR -->
<div>{typeof window !== 'undefined' ? clientData : serverData}</div>
```

#### 2. "Cannot read property of undefined"

**Errore**:
```
TypeError: Cannot read property 'title' of undefined
```

**Soluzioni**:
```astro
<!-- Usa optional chaining -->
<h1>{post?.data?.title}</h1>

<!-- Oppure controllo condizionale -->
{post && post.data && (
  <h1>{post.data.title}</h1>
)}
```

### Performance Issues

#### 1. "Slow page load"

**Diagnosi**:
```bash
# Analizza bundle size
npm run build
npx bundlesize

# Test performance
npm run lighthouse
```

**Soluzioni**:
```astro
<!-- Lazy load immagini -->
<img loading="lazy" src="/image.jpg" alt="Description" />

<!-- Preload risorse critiche -->
<link rel="preload" href="/critical.css" as="style" />

<!-- Code splitting -->
<Component client:visible />
```

#### 2. "Large bundle size"

**Soluzioni**:
```javascript
// astro.config.mjs - Manual chunks
export default defineConfig({
  vite: {
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
            utils: ['lodash', 'date-fns']
          }
        }
      }
    }
  }
});
```

### SEO Issues

#### 1. "Missing meta tags"

**Verifica**:
```bash
# Test SEO
curl -s https://tuodominio.com | grep -i "meta"
```

**Soluzioni**:
```astro
<!-- In BaseHead.astro -->
<meta name="description" content={description} />
<meta property="og:title" content={title} />
<meta property="og:description" content={description} />
<meta name="twitter:card" content="summary_large_image" />
```

#### 2. "Duplicate content"

**Soluzioni**:
```astro
<!-- Canonical URL -->
<link rel="canonical" href={canonicalURL} />

<!-- Robots meta -->
<meta name="robots" content="index, follow" />
```

## 🔍 Debug Tools

### Astro Debug

```bash
# Debug mode
DEBUG=astro:* npm run dev

# Verbose build
npm run build -- --verbose

# Check configuration
npx astro info
```

### Browser DevTools

```javascript
// Console debugging
console.log('Debug info:', data);

// Performance monitoring
performance.mark('start');
// ... code ...
performance.mark('end');
performance.measure('operation', 'start', 'end');
```

### Network Issues

```bash
# Test connectivity
ping tuodominio.com

# Check DNS
nslookup tuodominio.com

# Test SSL
openssl s_client -connect tuodominio.com:443
```

## 📊 Monitoring

### Error Tracking

```javascript
// Sentry integration
import * as Sentry from '@sentry/astro';

Sentry.init({
  dsn: 'YOUR_SENTRY_DSN',
  environment: import.meta.env.MODE
});
```

### Performance Monitoring

```javascript
// Web Vitals
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

function sendToAnalytics(metric) {
  gtag('event', metric.name, {
    value: Math.round(metric.value),
    event_category: 'Web Vitals'
  });
}

getCLS(sendToAnalytics);
getFID(sendToAnalytics);
getFCP(sendToAnalytics);
getLCP(sendToAnalytics);
getTTFB(sendToAnalytics);
```

## 🆘 Emergency Procedures

### Site Down

1. **Check hosting status**
   ```bash
   # Vercel
   curl -I https://vercel-status.com
   
   # Netlify
   curl -I https://netlifystatus.com
   ```

2. **Rollback deployment**
   ```bash
   # Git rollback
   git revert HEAD
   git push origin main
   
   # Platform rollback
   vercel rollback [deployment-url]
   ```

3. **Enable maintenance mode**
   ```html
   <!-- public/maintenance.html -->
   <!DOCTYPE html>
   <html>
   <head>
     <title>Manutenzione in corso</title>
   </head>
   <body>
     <h1>Sito in manutenzione</h1>
     <p>Torneremo presto online!</p>
   </body>
   </html>
   ```

### Data Loss

1. **Restore from backup**
   ```bash
   # Git history
   git log --oneline
   git checkout [commit-hash] -- src/content/
   
   # Database backup
   psql $DATABASE_URL < backup.sql
   ```

2. **Content recovery**
   ```bash
   # From cache
   curl -s "https://web.archive.org/web/*/tuodominio.com"
   
   # From CDN
   curl -s "https://cdn.tuodominio.com/content.json"
   ```

## 📞 Getting Help

### Community Support

- **Astro Discord**: [astro.build/chat](https://astro.build/chat)
- **GitHub Discussions**: [github.com/withastro/astro/discussions](https://github.com/withastro/astro/discussions)
- **Stack Overflow**: Tag `astro`

### Professional Support

- **Astro Team**: [astro.build/support](https://astro.build/support)
- **Hosting Support**:
  - Vercel: [vercel.com/support](https://vercel.com/support)
  - Netlify: [netlify.com/support](https://netlify.com/support)

### Creating Bug Reports

```markdown
## Bug Report Template

**Describe the bug**
A clear description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. See error

**Expected behavior**
What you expected to happen.

**Environment**
- OS: [e.g. macOS 12.0]
- Node.js: [e.g. 18.14.1]
- Astro: [e.g. 4.0.0]
- Browser: [e.g. Chrome 120]

**Additional context**
Any other context about the problem.
```

---

## 🔄 Preventive Measures

### Regular Maintenance

```bash
#!/bin/bash
# maintenance.sh

echo "🔧 Running maintenance tasks..."

# Update dependencies
npm update

# Check for vulnerabilities
npm audit

# Clean cache
rm -rf node_modules/.astro
rm -rf dist

# Test build
npm run build

# Run tests
npm test

echo "✅ Maintenance completed"
```

### Monitoring Setup

```yaml
# .github/workflows/monitor.yml
name: Site Monitoring

on:
  schedule:
    - cron: '0 */6 * * *'  # Every 6 hours

jobs:
  monitor:
    runs-on: ubuntu-latest
    steps:
      - name: Check site status
        run: |
          response=$(curl -s -o /dev/null -w "%{http_code}" https://tuodominio.com)
          if [ $response != "200" ]; then
            echo "Site is down! Status: $response"
            exit 1
          fi
```

## 🔧 Problemi API e Form (Nuove Funzionalità)

### API Routes non funzionano (404 Error)

**Sintomi**: `POST /api/contact 404 (Not Found)` o `POST /api/newsletter 404`

**Causa**: Astro configurato per output statico invece di hybrid

**Soluzione**:
1. **Verifica configurazione Astro**:
   ```javascript
   // astro.config.mjs
   export default defineConfig({
     output: 'hybrid', // ✅ Deve essere 'hybrid'
     adapter: node({ mode: 'standalone' }),
     // ...
   });
   ```

2. **Verifica adapter Node.js**:
   ```bash
   npm install @astrojs/node
   ```

3. **Verifica prerender nelle API**:
   ```typescript
   // src/pages/api/contact.ts
   export const prerender = false; // ✅ Deve essere false
   ```

### Privacy Modal non si apre

**Sintomi**: `ReferenceError: openPrivacyModal is not defined`

**Causa**: Funzioni JavaScript non globali per onclick handlers

**Soluzione**:
```javascript
// ❌ Errato
function openPrivacyModal() { ... }

// ✅ Corretto
window.openPrivacyModal = function() { ... }
```

### Back to Top non appare

**Sintomi**: Il pulsante "Torna su" non si mostra durante lo scroll

**Soluzione**:
1. **Verifica JavaScript**:
   ```javascript
   window.addEventListener('scroll', function() {
     if (window.pageYOffset > 300) {
       backToTopBtn.classList.add('visible');
     }
   });
   ```

2. **Verifica CSS**:
   ```css
   .back-to-top.visible {
     opacity: 1;
     visibility: visible;
   }
   ```

### Ricerca blog non funziona

**Sintomi**: Nessun feedback visivo durante la ricerca

**Soluzione**:
1. **Verifica data attributes**:
   ```html
   <article data-post-title="..." data-post-description="...">
   ```

2. **Verifica JavaScript**:
   ```javascript
   const posts = document.querySelectorAll('.blog-post-item');
   // Assicurati che gli elementi esistano
   ```

### Caricamento progressivo non funziona

**Sintomi**: Il pulsante "Carica altri" non appare o non funziona

**Soluzione**:
1. **Verifica array posts**:
   ```javascript
   const allPosts = allPostsData; // Deve essere definito
   ```

2. **Verifica elementi DOM**:
   ```javascript
   const loadMoreBtn = document.getElementById('load-more-btn');
   const postsContainer = document.getElementById('posts-container');
   ```

---

**Questa guida dovrebbe coprire la maggior parte dei problemi comuni. Per problemi specifici, consulta la documentazione Astro o apri un issue su GitHub.**
