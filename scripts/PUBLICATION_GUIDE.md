# 🎛️ Guida alla Gestione Pubblicazione Blog

Questo sistema ti permette di controllare completamente quali articoli importati da Strapi sono visibili sul tuo blog.

## 🔄 Flusso di Lavoro

### 1. Importazione da Strapi
```bash
npm run sync-blog
```
- Tutti gli articoli vengono importati come **DRAFT** (bozze)
- Non sono visibili sul sito web
- Puoi rivederli e decidere cosa pubblicare

### 2. Gestione Pubblicazione
```bash
npm run manage-blog
```
- Interfaccia interattiva per gestire gli articoli
- Cambia stato: DRAFT → LIVE → PAUSED

### 3. Pubblicazione sul Sito
- Solo gli articoli con `published: true` e `draft: false` sono visibili
- Il sistema filtra automaticamente gli articoli

## 📊 Stati degli Articoli

### 📝 DRAFT (Bozza)
- **Stato**: `draft: true`, `published: false`
- **Visibilità**: ❌ Non visibile sul sito
- **Uso**: Articoli importati, in revisione, non pronti

### ✅ LIVE (Pubblicato)
- **Stato**: `draft: false`, `published: true`
- **Visibilità**: ✅ Visibile sul sito
- **Uso**: Articoli pronti per il pubblico

### ⏸️ PAUSED (In Pausa)
- **Stato**: `draft: false`, `published: false`
- **Visibilità**: ❌ Non visibile sul sito
- **Uso**: Articoli temporaneamente nascosti

## 🎮 Comandi Disponibili

### Gestione Interattiva
```bash
npm run manage-blog
```
Interfaccia completa con menu interattivo:
- Lista tutti gli articoli con stato
- Cambia stato con comandi semplici
- Aggiornamento in tempo reale

### Lista Articoli
```bash
npm run blog-list
```
Mostra tabella con tutti gli articoli e il loro stato attuale.

### Pubblicazione Rapida
```bash
# Pubblica un articolo specifico
npm run blog-publish 3

# Pubblica tutte le bozze
npm run blog-publish-all
```

### Comandi Diretti
```bash
# Imposta come bozza
node scripts/blog-manager.js draft 2

# Metti in pausa
node scripts/blog-manager.js pause 4

# Pubblica
node scripts/blog-manager.js publish 1
```

## 💻 Uso nell'Interfaccia Interattiva

```
📚 Articoli del Blog:

ID  | Stato      | Titolo                                    | Data
----|------------|-------------------------------------------|------------
 1  | ✅ LIVE    | Come Ottimizzare le Performance Web       | 15/01/2024
 2  | 📝 DRAFT   | Guida Completa a React Hooks             | 14/01/2024
 3  | ⏸️  PAUSED  | Tutorial JavaScript Avanzato             | 13/01/2024

Comandi disponibili:
  [numero] publish  - Pubblica articolo
  [numero] draft    - Imposta come bozza
  [numero] pause    - Metti in pausa
  refresh           - Aggiorna lista
  quit              - Esci

> 2 publish
✅ Articolo "Guida Completa a React Hooks" impostato come PUBBLICATO
```

## 🔧 Integrazione nel Codice Astro

### Filtrare Articoli Pubblicati
```typescript
import { getCollection } from 'astro:content';
import { getPublishedPosts } from '../utils/blog-filters';

// Ottieni solo articoli pubblicati
const allPosts = await getCollection('blog');
const publishedPosts = getPublishedPosts(allPosts);
```

### Esempio Completo
```astro
---
import { getCollection } from 'astro:content';
import { getPublishedPosts, getFeaturedPosts } from '../utils/blog-filters';

const allPosts = await getCollection('blog');
const publishedPosts = getPublishedPosts(allPosts);
const featuredPosts = getFeaturedPosts(allPosts);
---

<main>
  <!-- Solo articoli pubblicati -->
  {publishedPosts.map(post => (
    <article>
      <h2>{post.data.title}</h2>
      <p>{post.data.description}</p>
    </article>
  ))}
</main>
```

## 🎯 Casi d'Uso Comuni

### 1. Revisione Prima della Pubblicazione
```bash
# 1. Importa da Strapi (tutto come DRAFT)
npm run sync-blog

# 2. Rivedi gli articoli
npm run blog-list

# 3. Pubblica selettivamente
npm run manage-blog
> 1 publish
> 3 publish
```

### 2. Pubblicazione Programmata
```bash
# Importa articoli come DRAFT
npm run sync-blog

# Pubblica quando sei pronto
npm run blog-publish 2
```

### 3. Nascondere Temporaneamente
```bash
# Metti in pausa un articolo pubblicato
npm run manage-blog
> 5 pause
```

### 4. Pubblicazione di Massa
```bash
# Pubblica tutte le bozze in una volta
npm run blog-publish-all
```

## 📋 Frontmatter degli Articoli

### Articolo Importato (DRAFT)
```yaml
---
title: 'Titolo Articolo'
description: 'Descrizione'
pubDate: '2024-01-15'
draft: true          # ← Non visibile
published: false     # ← Non visibile
publishDate: '2024-01-15'
---
```

### Articolo Pubblicato (LIVE)
```yaml
---
title: 'Titolo Articolo'
description: 'Descrizione'
pubDate: '2024-01-15'
draft: false         # ← Visibile
published: true      # ← Visibile
publishDate: '2024-01-15'
---
```

### Articolo in Pausa (PAUSED)
```yaml
---
title: 'Titolo Articolo'
description: 'Descrizione'
pubDate: '2024-01-15'
draft: false         # ← Non è una bozza
published: false     # ← Ma non è pubblicato
publishDate: '2024-01-15'
---
```

## 🔍 Funzioni di Filtro Disponibili

```typescript
// Filtri base
getPublishedPosts(posts)  // Solo pubblicati
getDraftPosts(posts)      // Solo bozze
getPausedPosts(posts)     // Solo in pausa

// Filtri avanzati
getFeaturedPosts(posts)   // Solo in evidenza (tra i pubblicati)
sortPostsByDate(posts)    // Ordina per data
searchPosts(posts, query) // Ricerca testuale
getRelatedPosts(posts, currentPost) // Articoli correlati

// Utilità
paginatePosts(posts, page, perPage) // Paginazione
getBlogStats(posts)       // Statistiche complete
```

## 🚀 Automazione Avanzata

### Script Personalizzato
Crea il tuo script per automazioni specifiche:

```javascript
import BlogManager from './scripts/blog-manager.js';

const manager = new BlogManager();

// Pubblica automaticamente articoli con tag specifico
const articles = await manager.listArticles();
const techArticles = articles.filter(a => 
  a.frontmatter?.tags?.includes('tech')
);

for (const article of techArticles) {
  await manager.changeArticleStatus(article.index, 'publish');
}
```

### Integrazione CI/CD
```yaml
# .github/workflows/auto-publish.yml
name: Auto Publish Blog
on:
  schedule:
    - cron: '0 9 * * 1'  # Ogni lunedì alle 9:00

jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - run: npm ci
      - run: npm run sync-blog
      - run: npm run blog-publish-all  # Pubblica tutto
      - uses: stefanzweifel/git-auto-commit-action@v4
```

## 🛡️ Best Practices

1. **Sempre Rivedere**: Non pubblicare automaticamente da Strapi
2. **Backup**: Il sistema crea backup automatici
3. **Test Locale**: Testa gli articoli in locale prima di pubblicare
4. **Staging**: Usa un ambiente di staging per verifiche
5. **Monitoraggio**: Controlla regolarmente lo stato degli articoli

## 🆘 Risoluzione Problemi

### Articolo Non Visibile
1. Verifica lo stato: `npm run blog-list`
2. Controlla il frontmatter del file
3. Assicurati che `draft: false` e `published: true`

### Errori di Parsing
1. Controlla la sintassi YAML del frontmatter
2. Usa il blog manager per correggere: `npm run manage-blog`

### Sincronizzazione Problematica
1. Verifica la connessione Strapi
2. Controlla i log di sincronizzazione
3. Usa `npm run sync-blog [id]` per articoli specifici

Questo sistema ti dà il controllo completo sulla pubblicazione, mantenendo la flessibilità di importare tutto da Strapi e decidere cosa mostrare al pubblico! 🎯
