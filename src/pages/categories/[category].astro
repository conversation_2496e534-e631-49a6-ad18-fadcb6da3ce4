---
import Layout from '../../layouts/Layout.astro';
import CategoryTag from '../../components/CategoryTag.astro';
import FormattedDate from '../../components/FormattedDate.astro';
import { getAllPosts, getAllCategories } from '../../utils/blog';

export async function getStaticPaths() {
  const categories = await getAllCategories();
  return categories.map((category) => {
    const slug = category.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');
    return {
      params: { category: slug },
      props: { category, slug },
    };
  });
}

const { category } = Astro.props;
const allPosts = await getAllPosts();
const posts = allPosts.filter(post => 
  post.data.category?.toLowerCase() === category.toLowerCase()
);

const title = `Categoria: ${category} - <PERSON>`;
const description = `Tutti gli articoli nella categoria ${category}`;
---

<Layout title={title} description={description}>
  <main class="blog-main">
    <div class="container">
      <!-- Hero Section -->
      <section class="blog-hero">
        <div class="hero-content">
          <a href="/blog" class="back-link">← Torna al Blog</a>
          <h1>Categoria: {category}</h1>
          <p class="lead">{posts.length} articol{posts.length === 1 ? 'o' : 'i'} in questa categoria</p>
        </div>
      </section>

      <!-- Posts Grid -->
      <section class="posts-section">
        <div class="posts-grid">
          {posts.map((post) => (
            <article class="blog-post-item">
              <a href={`/blog/${post.slug}/`}>
                {post.data.heroImage && (
                  <div class="post-image">
                    <img src={post.data.heroImage} alt={post.data.title} />
                  </div>
                )}
                <div class="post-content">
                  <h3 class="post-title">{post.data.title}</h3>
                  <p class="post-description">{post.data.description}</p>
                  <div class="post-meta">
                    <FormattedDate date={post.data.pubDate} />
                    {post.data.author && (
                      <span class="author">di {post.data.author}</span>
                    )}
                  </div>
                  <div class="post-tags">
                    <CategoryTag name={category} type="category" size="small" />
                    {post.data.tags?.slice(0, 3).map(tag => (
                      <CategoryTag name={tag} type="tag" size="small" />
                    ))}
                  </div>
                </div>
              </a>
            </article>
          ))}
        </div>

        {posts.length === 0 && (
          <div class="no-posts">
            <h3>Nessun articolo trovato</h3>
            <p>Non ci sono ancora articoli in questa categoria.</p>
            <a href="/blog" class="btn btn-theme">Torna al Blog</a>
          </div>
        )}
      </section>
    </div>
  </main>
</Layout>

<style>
  .blog-main {
    padding-top: 2rem;
    min-height: 100vh;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  .blog-hero {
    text-align: center;
    padding: 3rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 1rem;
    margin-bottom: 3rem;
  }

  .back-link {
    display: inline-flex;
    align-items: center;
    color: var(--theme-color);
    text-decoration: none;
    font-weight: 500;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
  }

  .back-link:hover {
    transform: translateX(-4px);
    color: color-mix(in srgb, var(--theme-color), black 20%);
  }

  .hero-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--heading-color);
  }

  .lead {
    font-size: 1.2rem;
    color: var(--default-color);
    opacity: 0.8;
  }

  .posts-section {
    margin-bottom: 4rem;
  }

  .posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
  }

  .blog-post-item {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
  }

  .blog-post-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  }

  .blog-post-item a {
    display: block;
    text-decoration: none;
    color: inherit;
    height: 100%;
  }

  .post-image {
    height: 200px;
    overflow: hidden;
  }

  .post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .blog-post-item:hover .post-image img {
    transform: scale(1.05);
  }

  .post-content {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    height: calc(100% - 200px);
  }

  .post-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--heading-color);
    line-height: 1.4;
  }

  .post-description {
    color: var(--default-color);
    margin-bottom: 1rem;
    line-height: 1.6;
    flex-grow: 1;
  }

  .post-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: var(--default-color);
    opacity: 0.7;
  }

  .post-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .no-posts {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .no-posts h3 {
    color: var(--heading-color);
    margin-bottom: 1rem;
  }

  .no-posts p {
    color: var(--default-color);
    margin-bottom: 2rem;
  }

  @media (max-width: 768px) {
    .posts-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .hero-content h1 {
      font-size: 2rem;
    }

    .blog-hero {
      padding: 2rem 0;
    }
  }
</style>
